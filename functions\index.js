/* eslint-disable quotes */
/* eslint-disable camelcase */
/* eslint-disable comma-dangle */
/* eslint-disable indent */
/* eslint-disable object-curly-spacing */
/* eslint-disable require-jsdoc */
/* eslint-disable max-len */
const functions = require("firebase-functions/v1"); // Updated to v1 namespace
const admin = require("firebase-admin");
const stripe = require("stripe")("sk_test_51RQ6USP3SlpAdLZeIg7yq4dzUKr3gO6lRMMOvqiSa6O9Ch0RHlUWR91wADQ02mkuO5I19KXn0GfIgQhYNAZ7alUy00QSMMM0W2"); // Replace with your actual Stripe secret key
const axios = require("axios");

admin.initializeApp();
const db = admin.firestore();

// Import modules
// const offersModule = require("./offers");
const invoiceFunctions = require("./invoice_functions");

// Export module functions
// exports.verifyRazorpayPayment = offersModule.verifyRazorpayPayment;
// exports.confirmPaypalPayment = offersModule.confirmPaypalPayment;
exports.generateInvoicePdf = invoiceFunctions.generateInvoicePdf;
exports.autoGenerateInvoicePdf = invoiceFunctions.autoGenerateInvoicePdf;
exports.syncInvoiceStatus = invoiceFunctions.syncInvoiceStatus;
exports.syncWithdrawalInvoiceStatus = invoiceFunctions.syncWithdrawalInvoiceStatus;
exports.syncDepositInvoiceStatus = invoiceFunctions.syncDepositInvoiceStatus;

/**
 * Creates a Stripe payment intent
 */
exports.createStripePaymentIntent = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
  }

  const userId = context.auth.uid;
  const amount = data.amount;
  const currency = data.currency || "usd";

  if (!amount || amount <= 0) {
    throw new functions.https.HttpsError("invalid-argument", "Amount must be a positive number");
  }

  try {
    // Create a payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amount,
      currency: currency,
      metadata: {
        userId: userId,
      },
    });

    return {
      id: paymentIntent.id,
      clientSecret: paymentIntent.client_secret,
      amount: amount,
      currency: currency,
    };
  } catch (error) {
    console.error("Stripe payment intent creation error:", error);
    throw new functions.https.HttpsError("internal", `Stripe error: ${error.message}`);
  }
});

/**
 * Creates a Stripe payment method from card details
 */
exports.createStripePaymentMethod = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
  }

  const { card, billing_details } = data;

  if (!card || !card.number || !card.exp_month || !card.exp_year || !card.cvc) {
    throw new functions.https.HttpsError("invalid-argument", "Missing card details");
  }

  try {
    // Create a payment method
    const paymentMethod = await stripe.paymentMethods.create({
      type: 'card',
      card: {
        number: card.number,
        exp_month: card.exp_month,
        exp_year: card.exp_year,
        cvc: card.cvc,
      },
      billing_details: billing_details || {},
    });

    return {
      paymentMethodId: paymentMethod.id,
    };
  } catch (error) {
    console.error("Stripe payment method creation error:", error);
    return {
      error: error.message,
    };
  }
});

/**
 * Confirms a Stripe payment with a payment method
 */
exports.confirmStripePayment = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
  }

  const { paymentIntentId, paymentMethodId } = data;

  if (!paymentIntentId || !paymentMethodId) {
    throw new functions.https.HttpsError("invalid-argument", "Missing payment details");
  }

  try {
    // Confirm the payment intent
    const paymentIntent = await stripe.paymentIntents.confirm(paymentIntentId, {
      payment_method: paymentMethodId,
    });

    if (paymentIntent.status === 'succeeded') {
      return {
        success: true,
        status: paymentIntent.status,
      };
    } else {
      return {
        success: false,
        status: paymentIntent.status,
        error: `Payment not successful. Status: ${paymentIntent.status}`,
      };
    }
  } catch (error) {
    console.error("Stripe payment confirmation error:", error);
    return {
      success: false,
      error: error.message,
    };
  }
});

/**
 * Verifies a Stripe payment and adds funds to the user's account
 */
exports.verifyStripePayment = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
  }

  const userId = context.auth.uid;
  const { paymentIntentId, amount, bonusAmount = 0, totalAmount } = data;

  if (!paymentIntentId || !amount) {
    throw new functions.https.HttpsError("invalid-argument", "Missing payment details");
  }

  try {
    // Verify the payment intent status
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status !== "succeeded") {
      throw new functions.https.HttpsError(
        "failed-precondition",
        `Payment not successful. Status: ${paymentIntent.status}`
      );
    }

    // Process the payment in Firestore
    await db.runTransaction(async (transaction) => {
      const userRef = db.collection("users").doc(userId);
      const historyRef = db.collection("users").doc(userId).collection("history").doc();
      const idempotencyRef = db.collection("idempotency").doc(paymentIntentId);
      const messageRef = db.collection("users").doc(userId).collection("messages").doc();

      // Check if already processed
      const idempotencyDoc = await transaction.get(idempotencyRef);
      if (idempotencyDoc.exists) {
        return { success: true };
      }

      // Update idempotency
      transaction.set(idempotencyRef, {
        processed: true,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });

      // Calculate total amount (base amount + bonus)
      const finalAmount = bonusAmount > 0 ? totalAmount : amount;

      // Update user funds
      transaction.update(userRef, {
        buyerFunds: admin.firestore.FieldValue.increment(finalAmount)
      });

      // Get user data for invoice
      const userDoc = await transaction.get(userRef);
      const userData = userDoc.data();

      // Record transaction history
      const transactionId = `DEP${Date.now()}`;
      transaction.set(historyRef, {
        id: transactionId,
        description: bonusAmount > 0 ?
          `Funds Added via Stripe (Includes $${bonusAmount.toFixed(2)} bonus)` :
          "Funds Added via Stripe",
        amount: finalAmount,
        baseAmount: amount,
        bonusAmount: bonusAmount,
        as: "Buyer",
        status: "Completed",
        date: admin.firestore.FieldValue.serverTimestamp(),
        type: "Deposit",
      });

      // Store message
      transaction.set(messageRef, {
        message: bonusAmount > 0 ?
          `Successfully added $${amount.toFixed(2)} to your balance via Stripe with a bonus of $${bonusAmount.toFixed(2)}. Total: $${finalAmount.toFixed(2)}` :
          `Successfully added $${amount.toFixed(2)} to your balance via Stripe.`,
        read: false,
        senderId: "system",
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
      });

      // Create invoice
      const invoiceRef = db.collection("invoices").doc();
      transaction.set(invoiceRef, {
        invoiceId: invoiceRef.id,
        userId: userId,
        userEmail: userData.email || "",
        userName: userData.name || "",
        invoiceType: "deposit",
        transactionId: transactionId,
        amount: amount,
        fees: 0, // No fees for Stripe in this implementation
        totalAmount: finalAmount,
        paymentMethod: "Stripe",
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        status: "Completed",
        depositId: transactionId,
        depositDetails: {
          paymentIntentId: paymentIntentId,
          baseAmount: amount,
          bonusAmount: bonusAmount,
        },

        companyName: "Guest Posts Links",
        companyAddress: "123 Web Street, Internet City, 12345",
        companyEmail: "<EMAIL>",
        companyPhone: "+****************",
        companyWebsite: "https://guestpostlinks.com",
      });

      // Also add to user's invoices subcollection
      transaction.set(
        db.collection("users").doc(userId).collection("invoices").doc(invoiceRef.id),
        {
          invoiceId: invoiceRef.id,
          userId: userId,
          userEmail: userData.email || "",
          userName: userData.name || "",
          invoiceType: "deposit",
          transactionId: transactionId,
          amount: amount,
          fees: 0, // No fees for Stripe in this implementation
          totalAmount: finalAmount,
          paymentMethod: "Stripe",
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          status: "Completed",
          depositId: transactionId,
          depositDetails: {
            paymentIntentId: paymentIntentId,
            baseAmount: amount,
            bonusAmount: bonusAmount,
          },

          companyName: "Guest Posts Links",
          companyAddress: "123 Web Street, Internet City, 12345",
          companyEmail: "<EMAIL>",
          companyPhone: "+****************",
          companyWebsite: "https://guestpostlinks.com",
        }
      );
    });

    return { success: true };
  } catch (error) {
    console.error("Stripe verification error:", error);
    throw new functions.https.HttpsError("internal", `Stripe error: ${error.message}`);
  }
});

const paypalClientId = "Ad5SS6XP1eziFBc26-_PYEn9KBEhCDtHJd-CtNoVTiSvhIoXGrhZ7ZBN9YGvTXKhrxATLKte7swmW2DI";
const paypalSecret = "EIv-7oJBy8G6dCx-V200mRo3pqRfSfGuPOMgoxCIV3LwPavuTeKDITMe6nhoisZkIipZv6Y_6d6sB-MM";
const paypalUrl = "https://api-m.sandbox.paypal.com"; // Or live
// Cache PayPal access token with expiration
let paypalAccessToken = null;
let tokenExpiration = 0;

async function getPaypalAccessToken() {
  if (paypalAccessToken && tokenExpiration > Date.now()) {
    return paypalAccessToken;
  }
  const auth = Buffer.from(`${paypalClientId}:${paypalSecret}`).toString("base64");
  const response = await axios.post(
    `${paypalUrl}/v1/oauth2/token`,
    "grant_type=client_credentials",
    {
      headers: {
        "Authorization": `Basic ${auth}`,
        "Content-Type": "application/x-www-form-urlencoded",
      },
    },
  );
  paypalAccessToken = response.data.access_token;
  tokenExpiration = Date.now() + (response.data.expires_in * 1000 - 60000); // Refresh 1min early
  return paypalAccessToken;
}


// exports.addFundsPaypal = functions.https.onCall(async (data, context) => {
//   if (!context.auth) {
//     throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
//   }
//   const amount = parseFloat(data.amount);
//   if (!Number.isFinite(amount) || amount <= 0) {
//     throw new functions.https.HttpsError("invalid-argument", "Amount must be a positive number");
//   }
//   try {
//     const accessToken = await getPaypalAccessToken();
//     const orderResponse = await axios.post(
//         `${paypalUrl}/v2/checkout/orders`,
//         {
//           intent: "CAPTURE",
//           purchase_units: [{amount: {value: amount.toFixed(2), currency_code: "USD"}}],
//           application_context: {
//             return_url: functions.config().app.success_url, // e.g., https://yourapp.com/success
//             cancel_url: functions.config().app.cancel_url, // e.g., https://yourapp.com/cancel
//           },
//         },
//         {
//           headers: {"Authorization": `Bearer ${accessToken}`, "Content-Type": "application/json"},
//         }
//     );
//     return {
//       orderId: orderResponse.data.id,
//       approvalUrl: orderResponse.data.links.find((link) => link.rel === "approve").href,
//     };
//   } catch (error) {
//     throw new functions.https.HttpsError("internal", `PayPal error: ${error.message}`);
//   }
// });

// /**
//  * Confirms PayPal payment and updates buyerFunds.
//  */


exports.confirmPaypalPayment = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
  }

  const userId = context.auth.uid;
  const orderId = data.orderId;
  const amount = parseFloat(data.amount);
  const bonusAmount = parseFloat(data.bonusAmount || 0);
  const totalAmount = parseFloat(data.totalAmount || amount);

  if (!orderId || !Number.isFinite(amount) || amount <= 0) {
    throw new functions.https.HttpsError("invalid-argument", "Valid orderId and positive amount are required");
  }

  try {
    const accessToken = await getPaypalAccessToken();

    // Check order status before capture
    const orderResponse = await axios.get(
      `${paypalUrl}/v2/checkout/orders/${orderId}`,
      {
        headers: {
          "Authorization": `Bearer ${accessToken}`,
          "Content-Type": "application/json"
        },
      },
    );

    if (orderResponse.data.status !== "APPROVED") {
      throw new functions.https.HttpsError(
        "failed-precondition",
        `Order status is ${orderResponse.data.status}, cannot capture`
      );
    }

    // Verify order amount matches
    const orderAmount = parseFloat(orderResponse.data.purchase_units[0].amount.value);
    if (Math.abs(orderAmount - amount) > 0.01) {
      throw new functions.https.HttpsError("failed-precondition", "Order amount mismatch");
    }

    if (orderResponse.data.status === "APPROVED") {
      await db.runTransaction(async (transaction) => {
        const userRef = db.collection("users").doc(userId);
        const historyRef = db.collection("users").doc(userId).collection("history").doc();
        const idempotencyRef = db.collection("idempotency").doc(orderId);
        const messageRef = db.collection("users").doc(userId).collection("messages").doc();


        // Check if already processed
        const idempotencyDoc = await transaction.get(idempotencyRef);
        if (idempotencyDoc.exists) {
          return { success: true };
        }

        // Update idempotency
        transaction.set(idempotencyRef, {
          processed: true,
          timestamp: admin.firestore.FieldValue.serverTimestamp()
        });

        // Calculate total amount (base amount + bonus)
        const finalAmount = bonusAmount > 0 ? totalAmount : amount;

        // Update user funds
        transaction.update(userRef, {
          buyerFunds: admin.firestore.FieldValue.increment(finalAmount)
        });

        // Get user data for invoice
        const userDoc = await transaction.get(userRef);
        const userData = userDoc.data();

        // Record transaction history
        const transactionId = `DEP${Date.now()}`;
        transaction.set(historyRef, {
          id: transactionId,
          description: bonusAmount > 0 ?
            `Funds Added via PayPal (Includes $${bonusAmount.toFixed(2)} bonus)` :
            "Funds Added via PayPal",
          amount: finalAmount,
          baseAmount: amount,
          bonusAmount: bonusAmount,
          as: "Buyer",
          status: "Completed",
          date: admin.firestore.FieldValue.serverTimestamp(),
          type: "Deposit",
        });

        // Store message with timestamp as a top-level field
        transaction.set(messageRef, {
          message: bonusAmount > 0 ?
            `Successfully added $${amount.toFixed(2)} to your balance via PayPal with a bonus of $${bonusAmount.toFixed(2)}. Total: $${finalAmount.toFixed(2)}` :
            `Successfully added $${amount.toFixed(2)} to your balance via PayPal.`,
          read: false,
          senderId: "system",
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
        });

        // Create invoice
        const invoiceRef = db.collection("invoices").doc();
        transaction.set(invoiceRef, {
          invoiceId: invoiceRef.id,
          userId: userId,
          userEmail: userData.email || "",
          userName: userData.name || "",
          invoiceType: "deposit",
          transactionId: transactionId,
          amount: amount,
          fees: 0, // No fees for PayPal in this implementation
          totalAmount: finalAmount,
          paymentMethod: "PayPal",
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          status: "Completed",
          depositId: transactionId,
          depositDetails: {
            paypalOrderId: orderId,
            baseAmount: amount,
            bonusAmount: bonusAmount,
          },

          companyName: "Guest Posts Links",
          companyAddress: "123 Web Street, Internet City, 12345",
          companyEmail: "<EMAIL>",
          companyPhone: "+****************",
          companyWebsite: "https://guestpostlinks.com",
        });

        // Also add to user's invoices subcollection
        transaction.set(
          db.collection("users").doc(userId).collection("invoices").doc(invoiceRef.id),
          {
            invoiceId: invoiceRef.id,
            userId: userId,
            userEmail: userData.email || "",
            userName: userData.name || "",
            invoiceType: "deposit",
            transactionId: transactionId,
            amount: amount,
            fees: 0, // No fees for PayPal in this implementation
            totalAmount: finalAmount,
            paymentMethod: "PayPal",
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            status: "Completed",
            depositId: transactionId,
            depositDetails: {
              paypalOrderId: orderId,
              baseAmount: amount,
              bonusAmount: bonusAmount,
            },
            emailSent: false,
            companyName: "Guest Posts Links",
            companyAddress: "123 Web Street, Internet City, 12345",
            companyEmail: "<EMAIL>",
            companyPhone: "+****************",
            companyWebsite: "https://guestpostlinks.com",
          }
        );
      });

      return { success: true };
    }
  } catch (error) {
    console.error("PayPal capture error:", {
      message: error.message,
      response: error.response ? {
        status: error.response.status,
        data: error.response.data,
      } : "No response data",
      orderId: orderId,
      userId: userId,
      amount: amount,
      bonusAmount: bonusAmount,
      totalAmount: totalAmount
    });

    throw new functions.https.HttpsError(
      "internal",
      `PayPal confirmation error: ${error.message}`
    );
  }
});


// exports.confirmPaypalPayment = functions.https.onCall(async (data, context) => {
//   if (!context.auth) {
//     throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
//   }

//   const userId = context.auth.uid;
//   const orderId = data.orderId;
//   const amount = parseFloat(data.amount);

//   if (!orderId || !Number.isFinite(amount) || amount <= 0) {
//     throw new functions.https.HttpsError("invalid-argument", "Valid orderId and positive amount are required");
//   }

//   try {
//     const accessToken = await getPaypalAccessToken();

//     // 1. Get order details to extract authorization ID
//     const orderResponse = await axios.get(`${paypalUrl}/v2/checkout/orders/${orderId}`, {
//       headers: {
//         "Authorization": `Bearer ${accessToken}`,
//         "Content-Type": "application/json",
//       },
//     });

//     const orderData = orderResponse.data;

//     if (orderData.status !== "COMPLETED" && orderData.status !== "APPROVED") {
//       throw new functions.https.HttpsError("failed-precondition", `Order status is ${orderData.status}, cannot capture`);
//     }

//     const authorizationId = orderData.purchase_units[0].payments.authorizations[0].id;
//     if (!authorizationId) {
//       throw new functions.https.HttpsError("failed-precondition", "No authorization found for this order");
//     }

//     const orderAmount = parseFloat(orderData.purchase_units[0].amount.value);
//     if (Math.abs(orderAmount - amount) > 0.01) {
//       throw new functions.https.HttpsError("failed-precondition", "Order amount mismatch");
//     }

//     // 2. Capture the authorization
//     const captureResponse = await axios.post(
//       `${paypalUrl}/v2/payments/authorizations/${authorizationId}/capture`,
//       {}, // empty body
//       {
//         headers: {
//           "Authorization": `Bearer ${accessToken}`,
//           "Content-Type": "application/json",
//         },
//       }
//     );

//     const captureStatus = captureResponse.data.status;
//     if (captureStatus !== "COMPLETED") {
//       throw new functions.https.HttpsError("failed-precondition", `Capture failed with status: ${captureStatus}`);
//     }

//     // 3. Proceed with DB updates if capture is successful
//     await db.runTransaction(async (transaction) => {
//       const userRef = db.collection("users").doc(userId);
//       const historyRef = userRef.collection("history").doc();
//       const idempotencyRef = db.collection("idempotency").doc(orderId);
//       const messageRef = db.collection("messages").doc();

//       const idempotencyDoc = await transaction.get(idempotencyRef);
//       if (idempotencyDoc.exists) return { success: true };

//       transaction.set(idempotencyRef, {
//         processed: true,
//         timestamp: admin.firestore.FieldValue.serverTimestamp()
//       });

//       transaction.update(userRef, {
//         buyerFunds: admin.firestore.FieldValue.increment(amount)
//       });

//       transaction.set(historyRef, {
//         id: `DEP${Date.now()}`,
//         description: "Funds Added via PayPal (Authorized)",
//         amount,
//         status: "Completed",
//         date: admin.firestore.FieldValue.serverTimestamp(),
//         type: "Deposit",
//       });

//       transaction.set(messageRef, {
//         threadId: `thread_${userId}_system`,
//         participants: [userId, "system"],
//         messages: [{
//           sender: "system",
//           text: `Successfully added $${amount} to your balance via PayPal.`,
//         }],
//         timestamp: admin.firestore.FieldValue.serverTimestamp(),
//         relatedTo: orderId,
//       });
//     });

//     return { success: true };
//   } catch (error) {
//     console.error("PayPal authorization capture error:", {
//       message: error.message,
//       response: error.response ? {
//         status: error.response.status,
//         data: error.response.data,
//       } : "No response",
//       orderId,
//       userId,
//       amount,
//     });

//     throw new functions.https.HttpsError("internal", `PayPal confirmation error: ${error.message}`);
//   }
// });

/**
 * Processes payment for a new order, reserving funds.
 * Implements commission system where:
 * - Buyer pays the full price (basePrice + commission)
 * - Publisher receives only the basePrice
 * - Admin account receives the commission
 */
exports.processOrderPayment = functions.firestore.document("orders/{orderId}").onCreate(async (snap, context) => {
  const order = snap.data();
  const orderId = context.params.orderId;
  const buyerId = order.buyerId;
  const totalPrice = order.totalPrice;
  const basePrice = order.basePrice;
  const websiteOwnerId = order.publisherId;

  // Calculate commission (difference between totalPrice and basePrice)
  const commissionAmount = totalPrice - basePrice;

  // Validate required fields
  if (!buyerId || !totalPrice || !websiteOwnerId || !basePrice) {
    const errorMessage = `Missing required fields: ${!buyerId ? "buyerId " : ""}${!totalPrice ? "totalPrice " : ""}${!websiteOwnerId ? "publisherId" : ""}${!basePrice ? "basePrice" : ""}`;
    console.error(`Order ${orderId} failed: ${errorMessage}`);
    await snap.ref.update({
      paymentStatus: "Failed",
      status: "Pending",
      error: errorMessage,
    });
    return;
  }

  try {
    await db.runTransaction(async (transaction) => {
      // Fetch buyer data
      const buyerRef = db.collection("users").doc(buyerId);
      const buyerDoc = await transaction.get(buyerRef);
      if (!buyerDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Buyer not found");
      }
      const buyerData = buyerDoc.data();
      const buyerFunds = buyerData.buyerFunds || 0;

      // Fetch publisher data
      const publisherRef = db.collection("users").doc(websiteOwnerId);
      const publisherDoc = await transaction.get(publisherRef);
      if (!publisherDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Publisher not found");
      }

      // Fetch or create admin account for commissions
      const adminRef = db.collection("admin").doc("finances");
      const adminDoc = await transaction.get(adminRef);

      // Check buyer's funds
      if (buyerFunds < totalPrice) {
        transaction.update(snap.ref, {
          paymentStatus: "Failed",
          status: "Pending",
          error: "Insufficient funds",
        });
        return;
      }

      // Update buyer's funds - deduct the full price (basePrice + commission)
      transaction.update(buyerRef, {
        buyerFunds: admin.firestore.FieldValue.increment(-totalPrice),
        reservedFunds: admin.firestore.FieldValue.increment(totalPrice),
      });

      // Update publisher's reserved funds - only reserve the basePrice
      transaction.update(publisherRef, {
        reservedBalance: admin.firestore.FieldValue.increment(basePrice),
      });

      // Update admin account with commission
      if (adminDoc.exists) {
        transaction.update(adminRef, {
          balance: admin.firestore.FieldValue.increment(commissionAmount),
          totalCommissions: admin.firestore.FieldValue.increment(commissionAmount),
        });
      } else {
        transaction.set(adminRef, {
          balance: commissionAmount,
          totalCommissions: commissionAmount,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
        });
      }

      // Log buyer's history - show the full price
      transaction.set(db.collection("users").doc(buyerId).collection("history").doc(), {
        id: `ORD${Date.now()}`,
        description: `Order ${orderId} Placed`,
        amount: totalPrice,
        status: "Pending",
        date: admin.firestore.FieldValue.serverTimestamp(),
        type: "Order",
        as: "Buyer"
      });

      // Log commission in admin history
      transaction.set(db.collection("admin").doc("finances").collection("history").doc(), {
        id: `COM${Date.now()}`,
        description: `Commission from Order ${orderId}`,
        amount: commissionAmount,
        status: "Completed",
        date: admin.firestore.FieldValue.serverTimestamp(),
        type: "Commission",
        orderId: orderId,
      });

      // Update order status and add commission info
      transaction.update(snap.ref, {
        paymentStatus: "Paid",
        status: "Pending",
        commissionAmount: commissionAmount,
        publisherAmount: basePrice,
      });

      // We already have buyerDoc and buyerData from above

      // Create invoice for order
      const invoiceRef = db.collection("invoices").doc();
      transaction.set(invoiceRef, {
        invoiceId: invoiceRef.id,
        userId: buyerId,
        userEmail: buyerData.email || "",
        userName: buyerData.name || "",
        invoiceType: "order",
        transactionId: `ORD${Date.now()}`,
        amount: totalPrice,
        fees: 0, // No fees for orders
        totalAmount: totalPrice,
        paymentMethod: "Wallet",
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        status: "Pending",
        orderId: orderId,
        orderDetails: {
          websiteUrl: order.websiteUrl,
          websiteDomainName: order.websiteDomainName,
          postTitle: order.postTitle,
          backlinkType: order.backlinkType,
          basePrice: basePrice,
          commissionAmount: commissionAmount,
        },

        companyName: "Guest Posts Links",
        companyAddress: "123 Web Street, Internet City, 12345",
        companyEmail: "<EMAIL>",
        companyPhone: "+****************",
        companyWebsite: "https://guestpostlinks.com",
      });

      // Also add to user's invoices subcollection
      transaction.set(
        db.collection("users").doc(buyerId).collection("invoices").doc(invoiceRef.id),
        {
          invoiceId: invoiceRef.id,
          userId: buyerId,
          userEmail: buyerData.email || "",
          userName: buyerData.name || "",
          invoiceType: "order",
          transactionId: `ORD${Date.now()}`,
          amount: totalPrice,
          fees: 0, // No fees for orders
          totalAmount: totalPrice,
          paymentMethod: "Wallet",
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          status: "Completed",
          orderId: orderId,
          orderDetails: {
            websiteUrl: order.websiteUrl,
            websiteDomainName: order.websiteDomainName,
            postTitle: order.postTitle,
            backlinkType: order.backlinkType,
            basePrice: basePrice,
            commissionAmount: commissionAmount,
          },

          companyName: "Adsy Guest Posts",
          companyAddress: "123 Web Street, Internet City, 12345",
          companyEmail: "<EMAIL>",
          companyPhone: "+****************",
          companyWebsite: "https://adsy.com",
        }
      );

      // Create message thread document
      const messageRef = db.collection("messages").doc();
      transaction.set(messageRef, {
        threadId: `thread_${orderId}`,
        participants: [buyerId, websiteOwnerId, "admin"],
        relatedTo: orderId,
      });

      // Add initial message to a subcollection
      const initialMessageRef = messageRef.collection("messages").doc();
      transaction.set(initialMessageRef, {
        sender: "system",
        text: `Order ${orderId} placed for $${totalPrice}.`,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
      });
    });
  } catch (error) {
    console.error(`Order ${orderId} payment failed for buyer ${buyerId}: ${error.message}`, error.stack);
    await snap.ref.update({
      paymentStatus: "Failed",
      status: "Pending",
      error: error.message,
    });
  }
});
/**
 * Completes an order, transferring funds to the publisher.
 */


exports.cancelOrder = functions.firestore.document("orders/{orderId}").onUpdate(async (change, context) => {
  const before = change.before.data();
  const after = change.after.data();
  const orderId = context.params.orderId;

  // Only process if status changed to "Cancelled" or "Declined"
  if (before.status === after.status || !["Cancelled", "Declined"].includes(after.status)) {
    return;
  }

  const buyerId = after.buyerId;
  const publisherId = after.publisherId;
  const totalPrice = after.totalPrice;
  const basePrice = after.basePrice;
  const commissionAmount = after.commissionAmount || (totalPrice - basePrice);
  const publisherAmount = after.publisherAmount || basePrice;

  // Validate required fields
  if (!buyerId || !publisherId || !totalPrice || !basePrice) {
    const errorMessage = `Missing required fields: ${!buyerId ? "buyerId " : ""}${!publisherId ? "publisherId " : ""}${!totalPrice ? "totalPrice" : ""}${!basePrice ? "basePrice" : ""}`;
    console.error(`Order ${orderId} ${after.status.toLowerCase()} failed: ${errorMessage}`);
    await change.after.ref.update({
      paymentStatus: "Failed",
      status: after.status,
      error: errorMessage,
    });
    return;
  }

  try {
    await db.runTransaction(async (transaction) => {
      // Fetch buyer data
      const buyerRef = db.collection("users").doc(buyerId);
      const buyerDoc = await transaction.get(buyerRef);
      if (!buyerDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Buyer not found");
      }
      const buyerData = buyerDoc.data();
      const buyerReservedFunds = buyerData.reservedFunds || 0;

      // Fetch publisher data
      const publisherRef = db.collection("users").doc(publisherId);
      const publisherDoc = await transaction.get(publisherRef);
      if (!publisherDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Publisher not found");
      }
      const publisherData = publisherDoc.data();
      const publisherReservedFunds = publisherData.reservedBalance || 0;

      // Fetch admin account
      const adminRef = db.collection("admin").doc("finances");
      const adminDoc = await transaction.get(adminRef);

      if (!adminDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Admin account not found");
      }

      // Check if sufficient reserved funds exist
      if (buyerReservedFunds < totalPrice || publisherReservedFunds < publisherAmount) {
        transaction.update(change.after.ref, {
          paymentStatus: "Failed",
          status: after.status,
          error: "Insufficient reserved funds for refund",
        });
        return;
      }

      // Reverse buyer's funds - refund the full price
      transaction.update(buyerRef, {
        buyerFunds: admin.firestore.FieldValue.increment(totalPrice),
        reservedFunds: admin.firestore.FieldValue.increment(-totalPrice),
      });

      // Reverse publisher's reserved funds - only the base price
      transaction.update(publisherRef, {
        reservedBalance: admin.firestore.FieldValue.increment(-publisherAmount),
      });

      // Reverse admin's commission
      transaction.update(adminRef, {
        balance: admin.firestore.FieldValue.increment(-commissionAmount),
        totalCommissions: admin.firestore.FieldValue.increment(-commissionAmount),
      });

      // Log commission reversal in admin history
      transaction.set(db.collection("admin").doc("finances").collection("history").doc(), {
        id: `COMREV${Date.now()}`,
        description: `Commission reversed for Order ${orderId} (${after.status})`,
        amount: -commissionAmount,
        status: "Completed",
        date: admin.firestore.FieldValue.serverTimestamp(),
        type: "Commission Reversal",
        orderId: orderId,
      });

      // Log cancellation in buyer's history - show full price
      transaction.set(db.collection("users").doc(buyerId).collection("history").doc(), {
        id: `${after.status.toUpperCase()}${Date.now()}`,
        description: `Order ${orderId} ${after.status}`,
        amount: totalPrice,
        status: after.status,
        date: admin.firestore.FieldValue.serverTimestamp(),
        type: "Order",
        as: "Buyer"
      });

      // Log cancellation in publisher's history - show only base price
      transaction.set(db.collection("users").doc(publisherId).collection("history").doc(), {
        id: `${after.status.toUpperCase()}${Date.now()}`,
        description: `Order ${orderId} ${after.status}`,
        amount: publisherAmount,
        status: after.status,
        date: admin.firestore.FieldValue.serverTimestamp(),
        type: "Order",
        as: "Publisher"
      });

      // Update order status
      transaction.update(change.after.ref, {
        paymentStatus: "Refunded",
        status: after.status,
        error: null,
      });

      // Find the message thread for the order
      const messageQuery = await db.collection("messages")
        .where("threadId", "==", `thread_${orderId}`)
        .limit(1)
        .get();

      if (!messageQuery.empty) {
        const messageRef = messageQuery.docs[0].ref;
        // Add cancellation message to subcollection
        const cancelMessageRef = messageRef.collection("messages").doc();
        transaction.set(cancelMessageRef, {
          sender: "system",
          text: `Order ${orderId} ${after.status.toLowerCase()}. Funds of $${totalPrice} refunded.`,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
        });
      }
    });
  } catch (error) {
    console.error(`Order ${orderId} ${after.status.toLowerCase()} failed for buyer ${buyerId}: ${error.message}`, error.stack);
    await change.after.ref.update({
      paymentStatus: "Failed",
      status: after.status,
      error: error.message,
    });
  }
});


exports.completeOrder = functions.firestore.document("orders/{orderId}").onUpdate(async (change, context) => {
  const before = change.before.data();
  const after = change.after.data();
  const orderId = context.params.orderId;

  // Only process if status changed to "Completed"
  if (before.status === after.status || after.status !== "Completed") {
    return;
  }

  const buyerId = after.buyerId;
  const publisherId = after.publisherId;
  const totalPrice = after.totalPrice;
  const basePrice = after.basePrice;
  const commissionAmount = after.commissionAmount || (totalPrice - basePrice);
  const publisherAmount = after.publisherAmount || basePrice;

  // Validate required fields
  if (!buyerId || !publisherId || !totalPrice || !basePrice) {
    const errorMessage = `Missing required fields: ${!buyerId ? "buyerId " : ""}${!publisherId ? "publisherId " : ""}${!totalPrice ? "totalPrice" : ""}${!basePrice ? "basePrice" : ""}`;
    console.error(`Order ${orderId} completion failed: ${errorMessage}`);
    await change.after.ref.update({
      paymentStatus: "Failed",
      status: "Completed",
      error: errorMessage,
    });
    return;
  }

  try {
    await db.runTransaction(async (transaction) => {
      // Fetch buyer data
      const buyerRef = db.collection("users").doc(buyerId);
      const buyerDoc = await transaction.get(buyerRef);
      if (!buyerDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Buyer not found");
      }
      const buyerData = buyerDoc.data();
      const buyerReservedFunds = buyerData.reservedFunds || 0;

      // Fetch publisher data
      const publisherRef = db.collection("users").doc(publisherId);
      const publisherDoc = await transaction.get(publisherRef);
      if (!publisherDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Publisher not found");
      }
      const publisherData = publisherDoc.data();
      const publisherReservedFunds = publisherData.reservedBalance || 0;

      // Fetch admin account
      const adminRef = db.collection("admin").doc("finances");
      const adminDoc = await transaction.get(adminRef);

      if (!adminDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Admin account not found");
      }

      // Check if sufficient reserved funds exist
      if (buyerReservedFunds < totalPrice || publisherReservedFunds < publisherAmount) {
        transaction.update(change.after.ref, {
          paymentStatus: "Failed",
          status: "Completed",
          error: "Insufficient reserved funds for completion",
        });
        return;
      }

      // Transfer funds to publisher - only the base price
      transaction.update(publisherRef, {
        mainBalance: admin.firestore.FieldValue.increment(publisherAmount),
        reservedBalance: admin.firestore.FieldValue.increment(-publisherAmount),
      });

      // Clear buyer's reserved funds - the full price
      transaction.update(buyerRef, {
        reservedFunds: admin.firestore.FieldValue.increment(-totalPrice),
      });

      // Move commission from pending to confirmed in admin account
      // (No need to change the balance as it was already added when the order was created)
      transaction.update(adminRef, {
        confirmedCommissions: admin.firestore.FieldValue.increment(commissionAmount),
      });

      // Log commission confirmation in admin history
      transaction.set(db.collection("admin").doc("finances").collection("history").doc(), {
        id: `COMCONF${Date.now()}`,
        description: `Commission confirmed for Order ${orderId}`,
        amount: commissionAmount,
        status: "Completed",
        date: admin.firestore.FieldValue.serverTimestamp(),
        type: "Commission Confirmation",
        orderId: orderId,
      });

      // Log completion in buyer's history - show full price
      transaction.set(db.collection("users").doc(buyerId).collection("history").doc(), {
        id: `COMPLETE${Date.now()}`,
        description: `Order ${orderId} Completed`,
        amount: totalPrice,
        status: "Completed",
        date: admin.firestore.FieldValue.serverTimestamp(),
        type: "Order",
        as: "Buyer"
      });

      // Log completion in publisher's history - show only base price
      transaction.set(db.collection("users").doc(publisherId).collection("history").doc(), {
        id: `COMPLETE${Date.now()}`,
        description: `Order ${orderId} Completed`,
        amount: publisherAmount,
        status: "Completed",
        date: admin.firestore.FieldValue.serverTimestamp(),
        type: "Order",
        as: "Publisher"
      });

      // Update order status
      transaction.update(change.after.ref, {
        paymentStatus: "Paid",
        status: "Completed",
        error: null,
      });

      // Find the message thread for the order
      const messageQuery = await db.collection("messages")
        .where("threadId", "==", `thread_${orderId}`)
        .limit(1)
        .get();

      if (!messageQuery.empty) {
        const messageRef = messageQuery.docs[0].ref;
        // Add completion message to subcollection
        const completeMessageRef = messageRef.collection("messages").doc();
        transaction.set(completeMessageRef, {
          sender: "system",
          text: `Order ${orderId} completed. Funds of $${totalPrice} transferred to publisher.`,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
        });
      }
    });
  } catch (error) {
    console.error(`Order ${orderId} completion failed for buyer ${buyerId}: ${error.message}`, error.stack);
    await change.after.ref.update({
      paymentStatus: "Failed",
      status: "Completed",
      error: error.message,
    });
  }
});


// /**
//  * Requests a withdrawal, pending admin approval.
//  */

exports.requestWithdrawal = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
  }
  const userId = context.auth.uid;
  const amount = parseFloat(data.amount);
  const paypalEmail = data.paypalEmail;
  const minimumPayout = 60;
  if (!Number.isFinite(amount) || amount < minimumPayout) {
    throw new functions.https.HttpsError("invalid-argument", `Amount must be at least $${minimumPayout}`);
  }
  if (!paypalEmail || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(paypalEmail)) {
    throw new functions.https.HttpsError("invalid-argument", "Valid PayPal email is required");
  }
  try {
    const withdrawalId = `WTH${Date.now()}`;
    await db.runTransaction(async (transaction) => {
      const userRef = db.collection("users").doc(userId);
      const userDoc = await transaction.get(userRef);
      if (!userDoc.exists) {
        throw new functions.https.HttpsError("not-found", "User not found");
      }
      const mainBalance = userDoc.data().mainBalance || 0;
      if (mainBalance < amount) {
        throw new functions.https.HttpsError("invalid-argument", "Insufficient balance");
      }
      transaction.update(userRef, {
        mainBalance: admin.firestore.FieldValue.increment(-amount),
        reservedBalance: admin.firestore.FieldValue.increment(amount),
      });
      transaction.set(db.collection("withdrawals").doc(withdrawalId), {
        withdrawalId,
        userId,
        amount,
        paypalEmail,
        status: "Pending",
        requestedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      // Add to user history
      transaction.set(userRef.collection("history").doc(), {
        id: withdrawalId,
        description: `Withdrawal request for $${amount}`,
        amount,
        status: "Pending",
        date: admin.firestore.FieldValue.serverTimestamp(),
        type: "Withdrawal",
        as: "Publisher"
      });

      // Get user data for invoice
      const userData = userDoc.data();

      // Create invoice for withdrawal
      const invoiceRef = db.collection("invoices").doc();
      transaction.set(invoiceRef, {
        invoiceId: invoiceRef.id,
        userId: userId,
        userEmail: userData.email || "",
        userName: userData.name || "",
        invoiceType: "withdrawal",
        transactionId: withdrawalId,
        amount: amount,
        fees: 0, // No fees for withdrawals in this implementation
        totalAmount: amount,
        paymentMethod: "PayPal",
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        status: "Pending",
        withdrawalId: withdrawalId,
        withdrawalDetails: {
          paypalEmail: paypalEmail,
        },

        companyName: "Guest Posts Links",
        companyAddress: "123 Web Street, Internet City, 12345",
        companyEmail: "<EMAIL>",
        companyPhone: "+****************",
        companyWebsite: "https://guestpostlinks.com",
      });

      // Also add to user's invoices subcollection
      transaction.set(
        db.collection("users").doc(userId).collection("invoices").doc(invoiceRef.id),
        {
          invoiceId: invoiceRef.id,
          userId: userId,
          userEmail: userData.email || "",
          userName: userData.name || "",
          invoiceType: "withdrawal",
          transactionId: withdrawalId,
          amount: amount,
          fees: 0, // No fees for withdrawals in this implementation
          totalAmount: amount,
          paymentMethod: "PayPal",
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          status: "Pending",
          withdrawalId: withdrawalId,
          withdrawalDetails: {
            paypalEmail: paypalEmail,
          },

          companyName: "Guest Posts Links",
          companyAddress: "123 Web Street, Internet City, 12345",
          companyEmail: "<EMAIL>",
          companyPhone: "+****************",
          companyWebsite: "https://guestpostlinks.com",
        }
      );
      // Add messaging as a subcollection
      const messageRef = db
        .collection("messages")
        .doc(`thread_${withdrawalId}`)
        .collection("threadMessages")
        .doc();
      transaction.set(messageRef, {
        sender: "system",
        text: `Withdrawal request ${withdrawalId} for $${amount} submitted.`,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
      });
      // Set message thread metadata
      transaction.set(db.collection("messages").doc(`thread_${withdrawalId}`), {
        threadId: `thread_${withdrawalId}`,
        participants: [userId, "admin"],
        relatedTo: withdrawalId,
        lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
      });
    });
    return { success: true, withdrawalId };
  } catch (error) {
    console.error(`Withdrawal request failed: ${error.message}`);
    throw new functions.https.HttpsError("internal", `Withdrawal request failed: ${error.message}`);
  }
});

// /**
//  * Admin approves or rejects a withdrawal.
//  */

exports.approveWithdrawal = functions.https.onCall(async (data, context) => {
  const withdrawalId = data.withdrawalId;
  const approve = data.approve;
  try {
    const withdrawalRef = db.collection("withdrawals").doc(withdrawalId);
    const withdrawalDoc = await withdrawalRef.get();
    if (!withdrawalDoc.exists) {
      throw new functions.https.HttpsError("not-found", "Withdrawal not found");
    }
    const withdrawal = withdrawalDoc.data();
    if (withdrawal.status !== "Pending") {
      throw new functions.https.HttpsError("failed-precondition", "Withdrawal already processed");
    }

    if (!approve) {
      await db.runTransaction(async (transaction) => {
        const userRef = db.collection("users").doc(withdrawal.userId);
        transaction.update(withdrawalRef, {
          status: "Rejected",
          rejectedAt: admin.firestore.FieldValue.serverTimestamp(),
          rejectionReason: data.reason || "Not specified",
        });
        transaction.update(userRef, {
          mainBalance: admin.firestore.FieldValue.increment(withdrawal.amount),
          reservedBalance: admin.firestore.FieldValue.increment(-withdrawal.amount),
        });
        transaction.set(
          db.collection("users").doc(withdrawal.userId).collection("history").doc(),
          {
            id: withdrawalId,
            description: `Withdrawal request ${withdrawalId} rejected: ${data.reason || "Not specified"}`,
            amount: withdrawal.amount,
            status: "Rejected",
            date: admin.firestore.FieldValue.serverTimestamp(),
            type: "Withdraw",
            as: "Publisher"

          },
        );
        // Add message to subcollection
        transaction.set(
          db.collection("messages").doc(`thread_${withdrawalId}`).collection("messages").doc(),
          {
            sender: "system",
            text: `Withdrawal ${withdrawalId} rejected: ${data.reason || "Not specified"}`,
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
          },
        );
      });
      return { success: true };
    }

    const fee = withdrawal.amount * 0.079;
    const netAmount = withdrawal.amount - fee;
    const accessToken = await getPaypalAccessToken();
    const payoutResponse = await axios.post(
      `${paypalUrl}/v1/payments/payouts`,
      {
        sender_batch_header: { sender_batch_id: withdrawalId, email_subject: "Your GuestPosts Withdrawal" },
        items: [
          {
            recipient_type: "EMAIL",
            amount: { value: netAmount.toFixed(2), currency: "USD" },
            receiver: withdrawal.paypalEmail,
            note: `Withdrawal from GuestPosts (ID: ${withdrawalId})`,
          },
        ],
      },
      {
        headers: { "Authorization": `Bearer ${accessToken}`, "Content-Type": "application/json" },
      },
    );

    if (payoutResponse.status === 201) {
      await db.runTransaction(async (transaction) => {
        const userRef = db.collection("users").doc(withdrawal.userId);
        transaction.update(withdrawalRef, {
          status: "Completed",
          completedAt: admin.firestore.FieldValue.serverTimestamp(),
          payoutId: payoutResponse.data.batch_header.payout_batch_id,
          amount: netAmount,
        });
        transaction.update(userRef, {
          reservedBalance: admin.firestore.FieldValue.increment(-withdrawal.amount),
        });
        transaction.set(
          db.collection("users").doc(withdrawal.userId).collection("history").doc(),
          {
            id: withdrawalId,
            description: "PayPal Withdrawal",
            amount: netAmount,
            status: "Completed",
            date: admin.firestore.FieldValue.serverTimestamp(),
            type: "Withdraw",
            as: "Publisher"

          },
        );
        // Add message to subcollection
        transaction.set(
          db.collection("messages").doc(`thread_${withdrawalId}`).collection("messages").doc(),
          {
            sender: "system",
            text: `Withdrawal ${withdrawalId} for $${netAmount} Completed.`,
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
          },
        );
      });
      return { success: true, withdrawalId };
    } else {
      throw new functions.https.HttpsError("internal", "Payout creation failed");
    }
  } catch (error) {
    console.error(`Withdrawal approval failed: ${error.message}`);
    throw new functions.https.HttpsError("internal", `Withdrawal approval failed: ${error.message}`);
  }
});

// /**
//  * Transfers funds from publisher's mainBalance to another user's buyerFunds.
//  */

exports.transferToBuyerFunds = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
  }
  const fromUserId = context.auth.uid;
  const toUserId = data.toUserId || fromUserId; // Support self or other user
  const amount = parseFloat(data.amount);
  if (!Number.isFinite(amount) || amount <= 0) {
    throw new functions.https.HttpsError("invalid-argument", "Amount must be a positive number");
  }
  try {
    const transferId = `TRF${Date.now()}`;
    await db.runTransaction(async (transaction) => {
      const fromUserRef = db.collection("users").doc(fromUserId);
      const toUserRef = db.collection("users").doc(toUserId);
      const fromUserDoc = await transaction.get(fromUserRef);
      const toUserDoc = await transaction.get(toUserRef);
      if (!fromUserDoc.exists || !toUserDoc.exists) {
        throw new functions.https.HttpsError("not-found", "User not found");
      }
      const mainBalance = fromUserDoc.data().mainBalance || 0;
      if (mainBalance < amount) {
        throw new functions.https.HttpsError("invalid-argument", "Insufficient balance");
      }
      transaction.update(fromUserRef, {
        mainBalance: admin.firestore.FieldValue.increment(-amount),
      });
      transaction.update(toUserRef, {
        buyerFunds: admin.firestore.FieldValue.increment(amount),
      });


      transaction.set(db.collection("users").doc(fromUserId).collection("history").doc(), {
        id: transferId,
        description: `Transfer to Buyer Funds (${toUserId}) as Publisher`,
        amount,
        status: "Completed",
        date: admin.firestore.FieldValue.serverTimestamp(),
        type: "Transfer",
        as: "Publisher"
      });


      transaction.set(db.collection("users").doc(toUserId).collection("history").doc(), {
        id: transferId,
        description: `Received Transfer from ${fromUserId} as Buyer`,
        amount,
        status: "Completed",
        date: admin.firestore.FieldValue.serverTimestamp(),
        type: "Transfer",
        as: "Buyer"

      });


      if (toUserId !== fromUserId) {
        transaction.set(db.collection("users").doc(toUserId).collection("history").doc(), {
          id: transferId,
          description: `Received Transfer from ${fromUserId}`,
          amount,
          status: "Completed",
          date: admin.firestore.FieldValue.serverTimestamp(),
          type: "Transfer",
          as: "Publisher"

        });
      }
      // Add messaging to a subcollection
      const messageRef = db.collection("messages").doc(`thread_${transferId}`);
      transaction.set(messageRef, {
        threadId: `thread_${transferId}`,
        participants: [fromUserId, toUserId, "admin"],
        relatedTo: transferId,
      });
      const messageSubRef = messageRef.collection("messages").doc();
      transaction.set(messageSubRef, {
        sender: "system",
        text: `Transfer ${transferId} of $${amount} from ${fromUserId} to ${toUserId} completed.`,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
      });
    });
    return { success: true, transferId };
  } catch (error) {
    console.error(`Transfer failed: ${error.message}`);
    throw new functions.https.HttpsError("internal", `Transfer failed: ${error.message}`);
  }
});

exports.verifyRazorpayPayment = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
  }

  const userId = context.auth.uid;
  const { payment_id, order_id, signature, amount, bonusAmount = 0, totalAmount } = data;

  if (!payment_id || !order_id || !signature || !amount) {
    throw new functions.https.HttpsError("invalid-argument", "Missing payment details");
  }

  try {
    // Verify signature (replace with your actual verification logic)
    const crypto = require("crypto");
    const secret = functions.config().razorpay.key_secret;
    const generated_signature = crypto
      .createHmac("sha256", secret)
      .update(order_id + "|" + payment_id)
      .digest("hex");

    if (generated_signature !== signature) {
      throw new functions.https.HttpsError("invalid-argument", "Invalid signature");
    }

    // Process the payment in Firestore
    await db.runTransaction(async (transaction) => {
      const userRef = db.collection("users").doc(userId);
      const historyRef = db.collection("users").doc(userId).collection("history").doc();
      const idempotencyRef = db.collection("idempotency").doc(payment_id);
      const messageRef = db.collection("users").doc(userId).collection("messages").doc();

      // Check if already processed
      const idempotencyDoc = await transaction.get(idempotencyRef);
      if (idempotencyDoc.exists) {
        return { success: true };
      }

      // Update idempotency
      transaction.set(idempotencyRef, {
        processed: true,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });

      // Calculate total amount (base amount + bonus)
      const finalAmount = bonusAmount > 0 ? totalAmount : amount;

      // Update user funds
      transaction.update(userRef, {
        buyerFunds: admin.firestore.FieldValue.increment(finalAmount)
      });

      // Record transaction history
      transaction.set(historyRef, {
        id: `DEP${Date.now()}`,
        description: bonusAmount > 0 ?
          `Funds Added via Razorpay (Includes $${bonusAmount.toFixed(2)} bonus)` :
          "Funds Added via Razorpay",
        amount: finalAmount,
        baseAmount: amount,
        bonusAmount: bonusAmount,
        as: "Buyer",
        status: "Completed",
        date: admin.firestore.FieldValue.serverTimestamp(),
        type: "Deposit",
      });

      // Store message
      transaction.set(messageRef, {
        message: bonusAmount > 0 ?
          `Successfully added $${amount.toFixed(2)} to your balance via Razorpay with a bonus of $${bonusAmount.toFixed(2)}. Total: $${finalAmount.toFixed(2)}` :
          `Successfully added $${amount.toFixed(2)} to your balance via Razorpay.`,
        read: false,
        senderId: "system",
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
      });
    });

    return { success: true };
  } catch (error) {
    console.error("Razorpay verification error:", error);
    throw new functions.https.HttpsError("internal", `Razorpay error: ${error.message}`);
  }
});

