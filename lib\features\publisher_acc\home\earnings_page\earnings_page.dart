import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts/core/utils/colors.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';
import 'package:guest_posts/core/theme/app_theme.dart';
import 'package:guest_posts/features/publisher_acc/home/<USER>/transfer_funds_dialog.dart';

// Models
class WithdrawalSettings {
  final double paypalFeePercentage;
  final double platformFee;
  final double razorpayFeePercentage;
  final double minimumPayout;

  WithdrawalSettings({
    required this.paypalFeePercentage,
    required this.platformFee,
    required this.razorpayFeePercentage,
    required this.minimumPayout,
  });

  factory WithdrawalSettings.fromMap(Map<String, dynamic> map) {
    return WithdrawalSettings(
      paypalFeePercentage:
          (map['paypalFeePercentage'] as num?)?.toDouble() ?? 0.04,
      platformFee: (map['platformFee'] as num?)?.toDouble() ?? 0.039,
      razorpayFeePercentage:
          (map['razorpayFeePercentage'] as num?)?.toDouble() ?? 0.04,
      minimumPayout: (map['minimumPayout'] as num?)?.toDouble() ?? 60.0,
    );
  }

  factory WithdrawalSettings.defaultSettings() {
    return WithdrawalSettings(
      paypalFeePercentage: 0.04,
      platformFee: 0.039,
      razorpayFeePercentage: 0.04,
      minimumPayout: 60.0,
    );
  }
}

class TransactionHistory {
  final String id;
  final String description;
  final double amount;
  final String status;
  final DateTime date;
  final String type;
  final String as;

  TransactionHistory({
    required this.id,
    required this.description,
    required this.amount,
    required this.status,
    required this.date,
    required this.type,
    required this.as,
  });

  factory TransactionHistory.fromMap(Map<String, dynamic> map) {
    return TransactionHistory(
      id: map['id'] ?? 'N/A',
      description: map['description'] ?? 'N/A',
      amount: (map['amount'] as num?)?.toDouble() ?? 0.0,
      status: map['status'] ?? 'Pending',
      date: (map['date'] as Timestamp?)?.toDate() ?? DateTime.now(),
      type: map['type'] ?? 'Unknown',
      as: map['as'] ?? 'Unknown',
    );
  }
}

// Services
class FirestoreService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Stream<DocumentSnapshot<Map<String, dynamic>>> streamDocument({
    required String collectionPath,
    required String documentId,
  }) {
    return _firestore.collection(collectionPath).doc(documentId).snapshots();
  }

  Stream<QuerySnapshot<Map<String, dynamic>>> streamSubCollection({
    required String collectionPath,
    required String documentId,
    required String subCollectionPath,
    String? orderBy,
    bool descending = true,
  }) {
    Query<Map<String, dynamic>> query = _firestore
        .collection(collectionPath)
        .doc(documentId)
        .collection(subCollectionPath);

    if (orderBy != null) {
      query = query.orderBy(orderBy, descending: descending);
    }

    return query.snapshots();
  }
}

class AdminService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Stream<WithdrawalSettings> getWithdrawalSettings() {
    return _firestore
        .collection('settings')
        .doc('defaults')
        .snapshots()
        .map((snapshot) => WithdrawalSettings.fromMap(snapshot.data() ?? {}));
  }
}

class TransactionService {
  final FirebaseFunctions _functions = FirebaseFunctions.instance;

  Future<Map<String, dynamic>> requestWithdrawal({
    required double amount,
    required String paymentEmail,
    required String paymentMethod,
  }) async {
    try {
      final callable = _functions.httpsCallable('requestWithdrawal');
      final response = await callable.call({
        'amount': amount,
        'paymentEmail': paymentEmail,
        'paymentMethod': paymentMethod,
      });
      return response.data;
    } catch (e) {
      rethrow;
    }
  }

  Future<Map<String, dynamic>> transferToBuyerFunds({
    required String userId,
    required double amount,
  }) async {
    try {
      final callable = _functions.httpsCallable('transferToBuyerFunds');
      final response = await callable.call({
        'toUserId': userId,
        'amount': amount,
      });
      return response.data;
    } catch (e) {
      rethrow;
    }
  }
}

// Refactored Main Widget
class EarningsPage extends StatefulWidget {
  const EarningsPage({super.key});

  @override
  State<EarningsPage> createState() => _EarningsPageState();
}

class _EarningsPageState extends State<EarningsPage> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirestoreService _firestoreService = FirestoreService();
  final AdminService _adminService = AdminService();
  final TransactionService _transactionService = TransactionService();

  bool _isLoading = false;
  String _selectedFilter = 'All';

  Future<void> _withdrawFunds(
      double availableBalance, WithdrawalSettings settings) async {
    if (availableBalance < settings.minimumPayout) {
      _showErrorSnackbar(
          'Available balance must be at least \$${settings.minimumPayout.toStringAsFixed(2)}');
      return;
    }

    final paymentEmailController = TextEditingController();
    final amountController = TextEditingController();
    String selectedPaymentMethod = 'PayPal'; // Default payment method

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Withdraw Funds',
              style: TextStyle(fontWeight: FontWeight.bold)),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Available: \$${availableBalance.toStringAsFixed(2)}',
                    style: const TextStyle(color: Colors.grey)),
                const SizedBox(height: 16),

                // Payment method selection
                const Text('Select Payment Method:',
                    style: TextStyle(fontWeight: FontWeight.w500)),
                const SizedBox(height: 8),
                _buildPaymentMethodSelector(
                  selectedPaymentMethod: selectedPaymentMethod,
                  onChanged: (value) {
                    setState(() => selectedPaymentMethod = value!);
                  },
                ),
                const SizedBox(height: 16),

                // Email field
                _buildTextField(
                  controller: paymentEmailController,
                  label: selectedPaymentMethod == 'PayPal'
                      ? 'PayPal Email'
                      : 'RazorPay Email',
                  icon: Icons.email,
                  iconColor: selectedPaymentMethod == 'PayPal'
                      ? Colors.blue
                      : AppTheme.accentColor,
                ),
                const SizedBox(height: 16),

                // Amount field
                _buildTextField(
                  controller: amountController,
                  label: 'Amount to Withdraw',
                  icon: Icons.attach_money,
                  keyboardType: TextInputType.number,
                ),

                // Fee information
                const SizedBox(height: 16),
                _buildFeeInfoBox(
                  selectedPaymentMethod: selectedPaymentMethod,
                  settings: settings,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
                onPressed: () => Navigator.pop(context),
                child:
                    const Text('Cancel', style: TextStyle(color: Colors.grey))),
            ElevatedButton(
              onPressed: _isLoading
                  ? null
                  : () => _processWithdrawal(
                        context: context,
                        paymentEmailController: paymentEmailController,
                        amountController: amountController,
                        selectedPaymentMethod: selectedPaymentMethod,
                        availableBalance: availableBalance,
                      ),
              style: ElevatedButton.styleFrom(
                backgroundColor: selectedPaymentMethod == 'PayPal'
                    ? Colors.blue[700]
                    : AppTheme.accentColor,
                foregroundColor: selectedPaymentMethod == 'PayPal'
                    ? Colors.white
                    : Colors.black87,
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2))
                  : const Text('Withdraw'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodSelector({
    required String selectedPaymentMethod,
    required Function(String?) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedPaymentMethod,
          isExpanded: true,
          items: ['PayPal', 'RazorPay']
              .map((method) => DropdownMenuItem(
                    value: method,
                    child: Row(
                      children: [
                        Icon(
                          method == 'PayPal'
                              ? FontAwesomeIcons.paypal
                              : FontAwesomeIcons.indianRupeeSign,
                          size: 18,
                          color: method == 'PayPal'
                              ? Colors.blue
                              : AppTheme.accentColor,
                        ),
                        const SizedBox(width: 10),
                        Text(method),
                      ],
                    ),
                  ))
              .toList(),
          onChanged: onChanged,
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    Color iconColor = Colors.blue,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: iconColor),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        filled: true,
        fillColor: Colors.grey[100],
      ),
      keyboardType: keyboardType,
    );
  }

  Widget _buildFeeInfoBox({
    required String selectedPaymentMethod,
    required WithdrawalSettings settings,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade100),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, size: 16, color: Colors.blue.shade700),
              const SizedBox(width: 8),
              Text(
                'Fee Information',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            selectedPaymentMethod == 'PayPal'
                ? '• PayPal Fee: \$${(settings.paypalFeePercentage).toStringAsFixed(1)}'
                : '• Stripe Fee: \$${(settings.razorpayFeePercentage).toStringAsFixed(1)}',
            style: TextStyle(fontSize: 13, color: Colors.blue.shade700),
          ),
          Text(
            '• Minimum withdrawal: \$${settings.minimumPayout.toStringAsFixed(2)}',
            style: TextStyle(fontSize: 13, color: Colors.blue.shade700),
          ),
        ],
      ),
    );
  }

  Future<void> _processWithdrawal({
    required BuildContext context,
    required TextEditingController paymentEmailController,
    required TextEditingController amountController,
    required String selectedPaymentMethod,
    required double availableBalance,
  }) async {
    final amount = double.tryParse(amountController.text) ?? 0;

    // Validate input
    if (amount <= 0 || amount > availableBalance) {
      _showErrorSnackbar('Invalid amount');
      return;
    }

    if (paymentEmailController.text.isEmpty ||
        !RegExp(r'^[^\s@]+@[^\s@]+\.[^\s@]+$')
            .hasMatch(paymentEmailController.text)) {
      _showErrorSnackbar('Invalid ${selectedPaymentMethod} email');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final response = await _transactionService.requestWithdrawal(
        amount: amount,
        paymentEmail: paymentEmailController.text,
        paymentMethod: selectedPaymentMethod,
      );

      if (response['success'] == true) {
        _showSuccessSnackbar(
            'Withdrawal request ${response['withdrawalId']} sent!');
      } else {
        throw Exception('Withdrawal request failed');
      }

      Navigator.pop(context);
    } catch (e) {
      _showErrorSnackbar('Error: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _transferToBuyerFunds(double availableBalance) async {
    try {
      final result = await showDialog<Map<String, dynamic>>(
        context: context,
        builder: (context) => TransferFundsDialog(
          availableBalance: availableBalance,
        ),
      );

      if (result != null && result['success'] == true) {
        if (mounted) {
          _showSuccessSnackbar(
            'Funds transferred successfully! Transfer ID: ${result['transferId']}',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackbar('Transfer failed: $e');
      }
    }
  }

  void _showSuccessSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_auth.currentUser == null) {
      return const Center(child: Text('Please sign in to view earnings'));
    }

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: StreamBuilder<DocumentSnapshot<Map<String, dynamic>>>(
        stream: _firestoreService.streamDocument(
          collectionPath: 'users',
          documentId: _auth.currentUser!.uid,
        ),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return _buildLoadingState();
          }

          if (!snapshot.hasData || !snapshot.data!.exists) {
            return const Center(child: Text('User data not found'));
          }

          final userData = snapshot.data!.data()!;
          if (userData['isPublisher'] != true) {
            return const Center(
                child: Text('This page is for publishers only'));
          }

          return StreamBuilder<WithdrawalSettings>(
            stream: _adminService.getWithdrawalSettings(),
            builder: (context, feesSnapshot) {
              final settings =
                  feesSnapshot.data ?? WithdrawalSettings.defaultSettings();

              return LayoutBuilder(
                builder: (context, constraints) {
                  final isWideScreen = constraints.maxWidth > 800;
                  return RefreshIndicator(
                    onRefresh: () async {
                      // Force a refresh of the state
                      setState(() {});
                      await Future.delayed(const Duration(milliseconds: 300));
                    },
                    child: SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: Padding(
                        padding: EdgeInsets.all(isWideScreen ? 24.0 : 16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildOverviewGrid(userData),
                            const SizedBox(height: 24),
                            isWideScreen
                                ? Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        flex: 2,
                                        child: _buildActionPanel(
                                          userData['mainBalance'] ?? 0.0,
                                          settings,
                                        ),
                                      ),
                                      const SizedBox(width: 24),
                                      Expanded(
                                        flex: 1,
                                        child:
                                            _buildHowItWorksSection(settings),
                                      ),
                                    ],
                                  )
                                : Column(
                                    children: [
                                      _buildActionPanel(
                                        userData['mainBalance'] ?? 0.0,
                                        settings,
                                      ),
                                      const SizedBox(height: 24),
                                      _buildHowItWorksSection(settings),
                                    ],
                                  ),
                            const SizedBox(height: 24),
                            PaymentHistorySection(
                              firestoreService: _firestoreService,
                              userId: _auth.currentUser!.uid,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildLoadingState() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Shimmer for overview grid
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: MediaQuery.of(context).size.width > 600 ? 3 : 1,
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                childAspectRatio: 3,
                children: List.generate(
                  3,
                  (index) => Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Shimmer for action panel and how it works
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 2,
                    child: Container(
                      height: 275,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 24),
                  Expanded(
                    flex: 1,
                    child: Container(
                      height: 275,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Shimmer for payment history
              Container(
                height: 400,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewGrid(Map<String, dynamic> userData) {
    return StreamBuilder<QuerySnapshot>(
      stream: _firestoreService.streamSubCollection(
        collectionPath: 'users',
        documentId: _auth.currentUser!.uid,
        subCollectionPath: 'history',
        orderBy: 'date',
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildShimmerGrid();
        }

        List<Map<String, dynamic>> history = [];
        if (snapshot.hasData && snapshot.data!.docs.isNotEmpty) {
          history = snapshot.data!.docs
              .map((doc) => doc.data() as Map<String, dynamic>)
              .toList();
        }

        final filteredHistory = _filterHistory(history);
        final totalOrderEarnings = filteredHistory.fold<double>(
            0.0,
            (sum, item) =>
                (item['type'] == 'Order' && item['as'] == 'Publisher')
                    ? sum + (item['amount'] as num).toDouble()
                    : sum);

        return GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: MediaQuery.of(context).size.width > 600 ? 3 : 1,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          childAspectRatio: 3,
          children: [
            _buildMetricCard(
                title: 'Total Order Earnings',
                value: totalOrderEarnings,
                icon: FontAwesomeIcons.dollarSign,
                color: Colors.blue,
                subtitle: 'Filtered order earnings'),
            _buildMetricCard(
                title: 'Available Balance',
                value: userData['mainBalance'] ?? 0.0,
                icon: FontAwesomeIcons.wallet,
                color: Colors.green,
                subtitle: 'Ready to withdraw'),
            _buildMetricCard(
                title: 'Reserved Balance',
                value: userData['reservedBalance'] ?? 0.0,
                icon: FontAwesomeIcons.lock,
                color: AppTheme.accentColor,
                subtitle: 'Pending payments'),
          ],
        );
      },
    );
  }

  Widget _buildShimmerGrid() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: GridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: MediaQuery.of(context).size.width > 600 ? 3 : 1,
        mainAxisSpacing: 16,
        crossAxisSpacing: 16,
        childAspectRatio: 3,
        children: List.generate(
          3,
          (index) => Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _filterHistory(
      List<Map<String, dynamic>> history) {
    final now = DateTime.now();
    switch (_selectedFilter) {
      case 'Last 30 Days':
        return history.where((entry) {
          final date = (entry['date'] as Timestamp).toDate();
          return date.isAfter(now.subtract(const Duration(days: 30)));
        }).toList();
      case 'Today':
        return history.where((entry) {
          final date = (entry['date'] as Timestamp).toDate();
          return date.year == now.year &&
              date.month == now.month &&
              date.day == now.day;
        }).toList();
      case 'This Year':
        return history.where((entry) {
          final date = (entry['date'] as Timestamp).toDate();
          return date.year == now.year;
        }).toList();
      default:
        return history;
    }
  }

  Widget _buildMetricCard({
    required String title,
    required double value,
    required IconData icon,
    required Color color,
    required String subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: AppTheme.accentColor),
          const SizedBox(height: 8),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const Spacer(),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '\$${value.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: const Color.fromARGB(255, 31, 21, 48),
                ),
              ),
              const SizedBox(height: 1),
              Text(
                subtitle,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.black.withOpacity(0.6),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionPanel(
      double availableBalance, WithdrawalSettings settings) {
    return Container(
      height: 275,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Actions',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  icon: FontAwesomeIcons.wallet,
                  label: 'Withdraw',
                  onTap: () => _withdrawFunds(availableBalance, settings),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildActionButton(
                  icon: FontAwesomeIcons.exchangeAlt,
                  label: 'Transfer to Funds',
                  onTap: () => _transferToBuyerFunds(availableBalance),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: _isLoading ? null : onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        height: 180,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 35, color: Colors.black87),
            const SizedBox(height: 12),
            Text(
              label,
              style: const TextStyle(
                fontFamily: 'Cairo',
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHowItWorksSection(WithdrawalSettings settings) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'How It Works',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildInfoRow(
            'Minimum Withdrawal',
            '\$${settings.minimumPayout.toStringAsFixed(2)}',
            FontAwesomeIcons.wallet,
          ),
          _buildInfoRow(
            'PayPal Fee',
            '\$${(settings.paypalFeePercentage).toStringAsFixed(1)}',
            FontAwesomeIcons.paypal,
          ),
          _buildInfoRow(
            'Stripe Fee',
            '\$${(settings.razorpayFeePercentage).toStringAsFixed(1)}',
            FontAwesomeIcons.stripeS,
          ),
          _buildInfoRow(
            'Platform Fee',
            '\$${(settings.platformFee).toStringAsFixed(1)}',
            FontAwesomeIcons.percentage,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Row(
              children: [
                Icon(
                  FontAwesomeIcons.calendarCheck,
                  size: 14,
                  color: Colors.blue,
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Payments processed: every Friday',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 14,
                      color: Colors.blue,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 16),
          const SizedBox(width: 8),
          Text(
            label,
            style: const TextStyle(
              fontFamily: 'Cairo',
              fontSize: 14,
            ),
          ),
          const Spacer(),
          Text(
            value,
            style: const TextStyle(
              fontFamily: 'Space',
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

class PaymentHistorySection extends StatefulWidget {
  final FirestoreService firestoreService;
  final String userId;

  const PaymentHistorySection({
    super.key,
    required this.firestoreService,
    required this.userId,
  });

  @override
  State<PaymentHistorySection> createState() => _PaymentHistorySectionState();
}

class _PaymentHistorySectionState extends State<PaymentHistorySection> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedTab = 'All';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Widget _buildTab(String title, bool isSelected) {
    return GestureDetector(
      onTap: () => setState(() => _selectedTab = title),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.accentColor : Colors.grey[200],
          borderRadius: BorderRadius.circular(10),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Colors.blue[700]!.withOpacity(0.2),
                    blurRadius: 4,
                  ),
                ]
              : null,
        ),
        child: Text(
          title,
          style: TextStyle(
            fontFamily: 'Cairo',
            color: isSelected ? Colors.white : Colors.black87,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isWideScreen = MediaQuery.of(context).size.width > 800;

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Payment History',
                style: TextStyle(
                  fontFamily: 'Space',
                  fontSize: isWideScreen ? 24 : 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(
                width: isWideScreen ? 300 : 200,
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    labelText: 'Search by ID',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.search),
                    filled: true,
                    fillColor: Colors.grey[100],
                  ),
                  onChanged: (value) => setState(() {}),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: ['All', 'Withdraw', 'Order', 'Transfer']
                  .map((tab) => Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: _buildTab(tab, _selectedTab == tab)))
                  .toList(),
            ),
          ),
          const SizedBox(height: 24),
          StreamBuilder<QuerySnapshot<Map<String, dynamic>>>(
            stream: widget.firestoreService.streamSubCollection(
              collectionPath: 'users',
              documentId: widget.userId,
              subCollectionPath: 'history',
              orderBy: 'date',
            ),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return _buildShimmerTable();
              }

              if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                return _buildEmptyState('No history available');
              }

              List<Map<String, dynamic>> history =
                  snapshot.data!.docs.map((doc) => doc.data()).toList();

              final searchQuery = _searchController.text.toLowerCase();
              history = history
                  .where((entry) =>
                      (entry['id'] ?? '')
                          .toString()
                          .toLowerCase()
                          .contains(searchQuery) &&
                      (_selectedTab == 'All' ||
                          entry['type'] == _selectedTab) &&
                      entry['as'] == "Publisher")
                  .toList();

              if (history.isEmpty) {
                return _buildEmptyState('No matching transactions found');
              }

              return SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: DataTable(
                  columnSpacing: isWideScreen ? 60 : 20,
                  columns: [
                    'ID',
                    'Description',
                    'Amount',
                    'Status',
                    'Date',
                    'Details'
                  ]
                      .map((label) => DataColumn(
                            label: Text(
                              label,
                              style: const TextStyle(
                                fontFamily: 'Space',
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ))
                      .toList(),
                  rows: history
                      .map((entry) => DataRow(cells: [
                            DataCell(Text(entry['id'] ?? 'N/A')),
                            DataCell(Text(entry['description'] ?? 'N/A')),
                            DataCell(Text(
                              '\$${((entry['amount'] as num?) ?? 0.0).toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontFamily: 'Space',
                                fontWeight: FontWeight.bold,
                              ),
                            )),
                            DataCell(
                                _buildStatusCell(entry['status'] ?? 'Pending')),
                            DataCell(
                                Text(_formatDate(entry['date'] as Timestamp?))),
                            DataCell(IconButton(
                              icon: const Icon(
                                Icons.info_outline,
                                color: Colors.blue,
                              ),
                              onPressed: () => _showDetailsDialog(entry),
                            )),
                          ]))
                      .toList(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStatusCell(String status) {
    Color backgroundColor;
    Color textColor;

    switch (status) {
      case 'Completed':
        backgroundColor = Colors.green.withOpacity(0.1);
        textColor = Colors.green;
        break;
      case 'Pending':
        backgroundColor = Colors.orange.withOpacity(0.1);
        textColor = Colors.orange;
        break;
      case 'Failed':
        backgroundColor = Colors.red.withOpacity(0.1);
        textColor = Colors.red;
        break;
      default:
        backgroundColor = Colors.grey.withOpacity(0.1);
        textColor = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: textColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';

    final date = timestamp.toDate();
    return DateFormat('yyyy-MM-dd').format(date);
  }

  Widget _buildShimmerTable() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            height: 50,
            color: Colors.white,
          ),
          const SizedBox(height: 16),

          // Rows
          ...List.generate(
            5,
            (index) => Padding(
              padding: const EdgeInsets.only(bottom: 12.0),
              child: Container(
                height: 40,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 40.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              FontAwesomeIcons.folderOpen,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDetailsDialog(Map<String, dynamic> entry) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isNarrow = screenWidth <= 600;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              FontAwesomeIcons.receipt,
              size: 20,
              color: AppTheme.accentColor,
            ),
            SizedBox(width: 12),
            Text(
              'Transaction Details',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isNarrow ? 18 : 22,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: isNarrow ? 16 : 24,
          vertical: 20,
        ),
        content: Container(
          width: isNarrow ? screenWidth * 0.85 : 500,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailCard(
                  'Transaction Info',
                  [
                    _buildDetailItem('ID', entry['id'] ?? 'N/A'),
                    _buildDetailItem('Type', entry['type'] ?? 'N/A'),
                    _buildDetailItem(
                        'Description', entry['description'] ?? 'N/A'),
                  ],
                ),
                SizedBox(height: 16),
                _buildDetailCard(
                  'Financial Details',
                  [
                    _buildDetailItem(
                      'Amount',
                      '\$${((entry['amount'] as num?) ?? 0.0).toStringAsFixed(2)}',
                    ),
                    _buildDetailItem(
                      'Status',
                      entry['status'] ?? 'N/A',
                      statusColor: _getStatusColor(entry['status'] ?? 'N/A'),
                    ),
                    _buildDetailItem(
                      'Date',
                      _formatDate(entry['date'] as Timestamp?),
                    ),
                  ],
                ),
                if (entry['paymentEmail'] != null ||
                    entry['paymentMethod'] != null ||
                    entry['notes'] != null) ...[
                  SizedBox(height: 16),
                  _buildDetailCard(
                    'Additional Information',
                    [
                      if (entry['paymentEmail'] != null)
                        _buildDetailItem(
                            'Payment Email', entry['paymentEmail']),
                      if (entry['paymentMethod'] != null)
                        _buildDetailItem(
                            'Payment Method', entry['paymentMethod']),
                      if (entry['notes'] != null)
                        _buildDetailItem('Notes', entry['notes']),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              backgroundColor: Colors.grey[100],
            ),
            child: Text(
              'Close',
              style: TextStyle(
                color: Colors.grey[800],
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  Widget _buildDetailCard(String title, List<Widget> items) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          Divider(),
          ...items,
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Completed':
        return Colors.green;
      case 'Pending':
        return Colors.orange;
      case 'Failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildDetailItem(String label, String value, {Color? statusColor}) {
    final isStatus = label == 'Status' && statusColor != null;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Cairo',
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 16),
          Flexible(
            child: isStatus
                ? Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      value,
                      style: TextStyle(
                        fontFamily: 'Space',
                        fontWeight: FontWeight.bold,
                        color: statusColor,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  )
                : Text(
                    value,
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.right,
                  ),
          ),
        ],
      ),
    );
  }
}

extension StringExtension on String {
  String capitalize() =>
      "${this[0].toUpperCase()}${substring(1).toLowerCase()}";
}
