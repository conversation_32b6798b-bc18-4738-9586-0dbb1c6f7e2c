import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts/core/theme/app_theme.dart';

/// A premium styled tab bar with underlined tabs and optional count badges
class PremiumTabBar extends StatelessWidget {
  final List<String> tabs;
  final List<int>? counts;
  final int selectedIndex;
  final Function(int) onTabSelected;
  final bool showCounts;
  final bool scrollable;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? labelPadding;

  const PremiumTabBar({
    Key? key,
    required this.tabs,
    required this.selectedIndex,
    required this.onTabSelected,
    this.counts,
    this.showCounts = false,
    this.scrollable = true,
    this.height,
    this.padding,
    this.labelPadding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppTheme.borderColor, width: 1),
        ),
      ),
      child: TabBar(
        tabAlignment: TabAlignment.start,
        dividerColor: const Color.fromARGB(255, 189, 189, 189),
        isScrollable: scrollable,
        padding: EdgeInsets.all(0),
        labelPadding: labelPadding ??
            const EdgeInsets.symmetric(horizontal: AppTheme.spacingL),
        tabs: List.generate(
          tabs.length,
          (index) => _buildTab(index),
        ),
        onTap: onTabSelected,
        indicator: UnderlineTabIndicator(
          borderSide: const BorderSide(
            color: AppTheme.accentColor,
            width: 3.0,
          ),
          insets: const EdgeInsets.symmetric(horizontal: 0.0),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        labelColor: AppTheme.accentColor,
        unselectedLabelColor: AppTheme.textPrimary,
        labelStyle: GoogleFonts.poppins(
          fontSize: AppTheme.fontSizeS,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: GoogleFonts.poppins(
          fontSize: AppTheme.fontSizeS,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildTab(int index) {
    final isSelected = index == selectedIndex;
    final hasCount = showCounts && counts != null && index < counts!.length;

    return Tab(
      height: 48,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(tabs[index]),
          if (hasCount && isSelected) ...[
            const SizedBox(width: AppTheme.spacingXS),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingXS,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                color: AppTheme.accentColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusRound),
              ),
              child: Text(
                counts![index].toString(),
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.accentColor,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
