import 'package:flutter/material.dart';

/// Modern icon set for the app
/// Uses rounded Material icons for a more modern look
class AppIcons {
  // Navigation icons
  static const IconData home = Icons.home_rounded;
  static const IconData search = Icons.search_rounded;
  static const IconData profile = Icons.person_rounded;
  static const IconData settings = Icons.settings_rounded;
  static const IconData menu = Icons.menu_rounded;
  static const IconData back = Icons.arrow_back_ios_rounded;
  static const IconData forward = Icons.arrow_forward_ios_rounded;
  static const IconData close = Icons.close_rounded;
  
  // Action icons
  static const IconData add = Icons.add_rounded;
  static const IconData edit = Icons.edit_rounded;
  static const IconData delete = Icons.delete_rounded;
  static const IconData save = Icons.save_rounded;
  static const IconData share = Icons.share_rounded;
  static const IconData download = Icons.download_rounded;
  static const IconData upload = Icons.upload_rounded;
  static const IconData refresh = Icons.refresh_rounded;
  static const IconData filter = Icons.filter_list_rounded;
  static const IconData sort = Icons.sort_rounded;
  static const IconData more = Icons.more_vert_rounded;
  static const IconData copy = Icons.content_copy_rounded;
  static const IconData paste = Icons.content_paste_rounded;
  
  // Communication icons
  static const IconData email = Icons.email_rounded;
  static const IconData message = Icons.message_rounded;
  static const IconData chat = Icons.chat_rounded;
  static const IconData call = Icons.call_rounded;
  static const IconData videocall = Icons.videocam_rounded;
  static const IconData notification = Icons.notifications_rounded;
  
  // Content icons
  static const IconData document = Icons.description_rounded;
  static const IconData image = Icons.image_rounded;
  static const IconData video = Icons.video_library_rounded;
  static const IconData attachment = Icons.attach_file_rounded;
  static const IconData link = Icons.link_rounded;
  static const IconData calendar = Icons.calendar_today_rounded;
  static const IconData time = Icons.access_time_rounded;
  static const IconData location = Icons.location_on_rounded;
  static const IconData category = Icons.category_rounded;
  static const IconData tag = Icons.label_rounded;
  
  // Status icons
  static const IconData success = Icons.check_circle_rounded;
  static const IconData error = Icons.error_rounded;
  static const IconData warning = Icons.warning_rounded;
  static const IconData info = Icons.info_rounded;
  static const IconData help = Icons.help_rounded;
  static const IconData verified = Icons.verified_rounded;
  static const IconData lock = Icons.lock_rounded;
  static const IconData unlock = Icons.lock_open_rounded;
  static const IconData visibility = Icons.visibility_rounded;
  static const IconData visibilityOff = Icons.visibility_off_rounded;
  
  // Finance icons
  static const IconData money = Icons.attach_money_rounded;
  static const IconData creditCard = Icons.credit_card_rounded;
  static const IconData wallet = Icons.account_balance_wallet_rounded;
  static const IconData bank = Icons.account_balance_rounded;
  static const IconData receipt = Icons.receipt_rounded;
  static const IconData shoppingCart = Icons.shopping_cart_rounded;
  static const IconData store = Icons.store_rounded;
  
  // Analytics icons
  static const IconData chart = Icons.bar_chart_rounded;
  static const IconData trending = Icons.trending_up_rounded;
  static const IconData trendingDown = Icons.trending_down_rounded;
  static const IconData analytics = Icons.analytics_rounded;
  static const IconData dashboard = Icons.dashboard_rounded;
  static const IconData insights = Icons.insights_rounded;
  
  // Web icons
  static const IconData web = Icons.language_rounded;
  static const IconData globe = Icons.public_rounded;
  static const IconData domain = Icons.domain_rounded;
  static const IconData cloud = Icons.cloud_rounded;
  static const IconData code = Icons.code_rounded;
  
  // Social icons
  static const IconData person = Icons.person_rounded;
  static const IconData people = Icons.people_rounded;
  static const IconData group = Icons.group_rounded;
  static const IconData follow = Icons.person_add_rounded;
  static const IconData like = Icons.favorite_rounded;
  static const IconData comment = Icons.comment_rounded;
  static const IconData star = Icons.star_rounded;
  static const IconData flag = Icons.flag_rounded;
  
  // Device icons
  static const IconData smartphone = Icons.smartphone_rounded;
  static const IconData tablet = Icons.tablet_rounded;
  static const IconData laptop = Icons.laptop_rounded;
  static const IconData desktop = Icons.desktop_windows_rounded;
  static const IconData tv = Icons.tv_rounded;
  static const IconData wifi = Icons.wifi_rounded;
  static const IconData bluetooth = Icons.bluetooth_rounded;
  
  // Misc icons
  static const IconData lightMode = Icons.light_mode_rounded;
  static const IconData darkMode = Icons.dark_mode_rounded;
  static const IconData translate = Icons.translate_rounded;
  static const IconData accessibility = Icons.accessibility_rounded;
  static const IconData bookmark = Icons.bookmark_rounded;
  static const IconData favorite = Icons.favorite_rounded;
  static const IconData thumbUp = Icons.thumb_up_rounded;
  static const IconData thumbDown = Icons.thumb_down_rounded;
}
