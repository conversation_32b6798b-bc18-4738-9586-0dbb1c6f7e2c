import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:guest_posts/core/models/website_model.dart';
import 'package:guest_posts/core/utils/colors.dart';
import 'package:language_picker/language_picker_dropdown.dart';
import 'package:language_picker/languages.dart';
import 'package:enhance_stepper/enhance_stepper.dart';
import 'package:multi_select_flutter/multi_select_flutter.dart';
import 'package:country_picker/country_picker.dart';
import 'package:guest_posts/core/utils/toast_helper.dart';
import 'package:guest_posts/core/theme/app_theme.dart';

class AddWebsitePage extends StatefulWidget {
  const AddWebsitePage({super.key});

  @override
  State<AddWebsitePage> createState() => _AddWebsitePageState();
}

class _AddWebsitePageState extends State<AddWebsitePage> {
  int _currentStep = 0;
  final _formKey = GlobalKey<FormState>();

  // Controllers
  final _urlController = TextEditingController();
  final _daController = TextEditingController();
  final _drController = TextEditingController();
  final _trafficController = TextEditingController();
  final _pricingController = TextEditingController();
  final _wordCountMinController = TextEditingController();
  final _wordCountMaxController = TextEditingController();
  final _maxLinksController = TextEditingController();
  final _allowedTopicsController = TextEditingController();
  final _disallowedTopicsController = TextEditingController();
  final _specialTopicsPriceController = TextEditingController();
  final _spamScoreController = TextEditingController();
  final _dofollowCostController = TextEditingController();

  // Local default values
  final Language _defaultLanguage = Languages.english;
  final bool _defaultIsSponsored = false;
  final String _defaultBacklinkType = 'dofollow';
  final double _defaultDofollowSurchargePercentage = 10.0;
  final double _defaultSpecialTopicsSurchargePercentage = 20.0;
  final int _defaultMaxCategories = 3;
  final double _defaultPricing = 100.0;
  final String _defaultTraffic = '10K';
  final int _defaultMinWordCount = 500;
  final int _defaultMaxWordCount = 2000;
  final int _defaultMaxLinks = 2;

  // Form variables
  Language? _selectedLanguage;
  bool _isSponsored = false;
  List<dynamic> _selectedCategories = [];
  final List<String> _allowedTopics = [];
  final List<String> _disallowedTopics = [];
  bool _enableSpecialTopicsPricing = false;
  String _backlinkType = '';
  double _dofollowSurchargePercentage = 0.0;
  double _specialTopicsSurchargePercentage = 0.0;
  int _maxCategories = 0;
  Country? _selectedCountry;

  // Categories for MultiSelect
  List<MultiSelectItem<dynamic>> _categories = [];

  @override
  void initState() {
    super.initState();

    // Initialize with local defaults
    _selectedLanguage = _defaultLanguage;
    _isSponsored = _defaultIsSponsored;
    _backlinkType = _defaultBacklinkType;
    _dofollowSurchargePercentage = _defaultDofollowSurchargePercentage;
    _specialTopicsSurchargePercentage =
        _defaultSpecialTopicsSurchargePercentage;
    _maxCategories = _defaultMaxCategories;
    _pricingController.text = _defaultPricing.toString();
    _trafficController.text = _defaultTraffic;
    _wordCountMinController.text = _defaultMinWordCount.toString();
    _wordCountMaxController.text = _defaultMaxWordCount.toString();
    _maxLinksController.text = _defaultMaxLinks.toString();

    _initializeCategories();
    _fetchDefaultsFromFirestore();
  }

  void _initializeCategories() {
    _categories = [
      MultiSelectItem('news', 'News'),
      MultiSelectItem('social', 'Social Media'),
      MultiSelectItem('ecommerce', 'E-commerce'),
      MultiSelectItem('education', 'Education'),
      MultiSelectItem('entertainment', 'Entertainment'),
      MultiSelectItem('travel', 'Travel'),
      MultiSelectItem('food', 'Food & Drink'),
      MultiSelectItem('sports', 'Sports'),
      MultiSelectItem('gaming', 'Gaming'),
      MultiSelectItem('fashion', 'Fashion'),
      MultiSelectItem('lifestyle', 'Lifestyle'),
      MultiSelectItem('business', 'Business'),
      MultiSelectItem('music', 'Music'),
      MultiSelectItem('photography', 'Photography'),
      MultiSelectItem('art', 'Art & Design'),
      MultiSelectItem('science', 'Science'),
      MultiSelectItem('government', 'Government'),
      MultiSelectItem('finance', 'Finance'),
      MultiSelectItem('technology', 'Technology'),
      MultiSelectItem('health', 'Health & Fitness'),
    ];
  }

  Future<void> _fetchDefaultsFromFirestore() async {
    try {
      DocumentSnapshot doc = await FirebaseFirestore.instance
          .collection('settings')
          .doc('defaults')
          .get();
      if (doc.exists) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        if (mounted) {
          setState(() {
            _backlinkType = data['defaultBacklinkType'] ?? _defaultBacklinkType;
            _dofollowSurchargePercentage =
                (data['dofollowSurchargePercentage'] ??
                        _defaultDofollowSurchargePercentage)
                    .toDouble();
            _specialTopicsSurchargePercentage =
                (data['specialTopicsSurchargePercentage'] ??
                        _defaultSpecialTopicsSurchargePercentage)
                    .toDouble();
            _maxCategories = data['maxCategories'] ?? _defaultMaxCategories;
            _selectedLanguage = Languages.defaultLanguages.firstWhere(
              (lang) =>
                  lang.name ==
                  (data['defaultLanguage'] ?? _defaultLanguage.name),
              orElse: () => _defaultLanguage,
            );
            _isSponsored = data['defaultIsSponsored'] ?? _defaultIsSponsored;
            _pricingController.text =
                (data['defaultPricing'] ?? _defaultPricing).toString();
            _trafficController.text = data['defaultTraffic'] ?? _defaultTraffic;
            _wordCountMinController.text =
                (data['defaultMinWordCount'] ?? _defaultMinWordCount)
                    .toString();
            _wordCountMaxController.text =
                (data['defaultMaxWordCount'] ?? _defaultMaxWordCount)
                    .toString();
            _maxLinksController.text =
                (data['defaultMaxLinks'] ?? _defaultMaxLinks).toString();
          });
        }
      }
    } catch (e) {
      // Error fetching defaults, will use local defaults instead
      ToastHelper.showError('Error loading defaults. Using default values.');
    }
  }

  @override
  void dispose() {
    _urlController.dispose();
    _daController.dispose();
    _drController.dispose();
    _trafficController.dispose();
    _pricingController.dispose();
    _wordCountMinController.dispose();
    _wordCountMaxController.dispose();
    _maxLinksController.dispose();
    _allowedTopicsController.dispose();
    _disallowedTopicsController.dispose();
    _specialTopicsPriceController.dispose();
    _spamScoreController.dispose();
    _dofollowCostController.dispose();
    super.dispose();
  }

  void _onStepContinue() {
    if (_formKey.currentState!.validate()) {
      if (_currentStep < _buildSteps().length - 1) {
        if (mounted) {
          setState(() => _currentStep++);
        }
      } else {
        _saveToFirestore();
      }
    }
  }

  void _onStepCancel() {
    if (_currentStep > 0) {
      if (mounted) {
        setState(() => _currentStep--);
      }
    }
  }

  Future<void> _saveToFirestore() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      double trafficValue = double.parse(
          _trafficController.text.replaceAll(RegExp(r'[^0-9.]'), ''));
      if (_trafficController.text.toLowerCase().contains('m')) {
        trafficValue *= 1000000;
      } else if (_trafficController.text.toLowerCase().contains('k')) {
        trafficValue *= 1000;
      }

      double basePrice = double.parse(_pricingController.text);
      double finalPrice = basePrice;

      if (_enableSpecialTopicsPricing) {
        if (_specialTopicsPriceController.text.isNotEmpty) {
          finalPrice += double.parse(_specialTopicsPriceController.text);
        } else {
          finalPrice += basePrice * (_specialTopicsSurchargePercentage / 100);
        }
      }

      if (_backlinkType == 'dofollow') {
        //   finalPrice *= (1 + (_dofollowSurchargePercentage / 100));
      }

      final website = WebsiteModel(
        spamScore: _spamScoreController.text.isNotEmpty
            ? double.parse(_spamScoreController.text)
            : null,
        dofollowCost: _dofollowCostController.text.isNotEmpty
            ? double.parse(_dofollowCostController.text)
            : 0.0,
        adminWebsite: false,
        websiteId: '',
        url: _urlController.text,
        domainName: _urlController.text.split('.').length > 1
            ? _urlController.text.split('.')[1]
            : _urlController.text,
        language: _selectedLanguage?.name,
        publisherId: FirebaseAuth.instance.currentUser?.uid,
        isSponsored: _isSponsored,
        categories: _selectedCategories.map((e) => e.toString()).toList(),
        da: int.parse(_daController.text),
        dr: int.parse(_drController.text),
        traffic: trafficValue.toInt(),
        pricing: finalPrice,
        basePricing: basePrice,
        specialTopicsAdditionalPrice: _enableSpecialTopicsPricing &&
                _specialTopicsPriceController.text.isNotEmpty
            ? double.parse(_specialTopicsPriceController.text)
            : (_enableSpecialTopicsPricing
                ? basePrice * (_specialTopicsSurchargePercentage / 100)
                : 0.0),
        hasSpecialTopicsPricing: _enableSpecialTopicsPricing,
        backlinkType: _backlinkType,
        allowedTopics: _allowedTopics,
        disallowedTopics: _disallowedTopics,
        wordCountMin: int.parse(_wordCountMinController.text),
        wordCountMax: int.parse(_wordCountMaxController.text),
        maxLinks: int.parse(_maxLinksController.text),
        status: 'Pending',
        submissionDate: Timestamp.now(),
        createdAt: Timestamp.now(),
        lastUpdated: Timestamp.now(),
        isActive: true,
        contentType: ['article'],
        contactEmail: FirebaseAuth.instance.currentUser?.email ?? '',
        notes: '',
        viewsCount: 0,
        clicksCount: 0,
        country: _selectedCountry?.name ?? '',
      );

      final DocumentReference docRef =
          await FirebaseFirestore.instance.collection('websites').add({
        ...website.toMap(),
        'submissionDate': FieldValue.serverTimestamp(),
        'createdAt': FieldValue.serverTimestamp(),
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      await docRef.update({'websiteId': docRef.id});

      ToastHelper.showSuccess(
          (_enableSpecialTopicsPricing || _backlinkType == 'dofollow')
              ? 'Website added with premium adjustments!'
              : 'Website added successfully!');
      _resetForm();
    } catch (e) {
      ToastHelper.showError('Error: $e');
    }
  }

  void _resetForm() {
    _formKey.currentState?.reset();
    _urlController.clear();
    _daController.clear();
    _drController.clear();
    _trafficController.text = _defaultTraffic;
    _pricingController.text = _defaultPricing.toString();
    _wordCountMinController.text = _defaultMinWordCount.toString();
    _wordCountMaxController.text = _defaultMaxWordCount.toString();
    _maxLinksController.text = _defaultMaxLinks.toString();
    _allowedTopicsController.clear();
    _disallowedTopicsController.clear();
    _specialTopicsPriceController.clear();
    _spamScoreController.clear();
    _dofollowCostController.clear();
    if (mounted) {
      setState(() {
        _currentStep = 0;
        _selectedLanguage = _defaultLanguage;
        _isSponsored = _defaultIsSponsored;
        _selectedCategories.clear();
        _allowedTopics.clear();
        _disallowedTopics.clear();
        _enableSpecialTopicsPricing = false;
        _backlinkType = _defaultBacklinkType;
        _selectedCountry = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Add website(s)',
              style: TextStyle(
                  fontFamily: 'Space',
                  fontSize: 30,
                  fontWeight: FontWeight.w700),
            ),
            const Divider(endIndent: 100, color: AppTheme.accentColor),
            const SizedBox(height: 10),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: EnhanceStepper(
                    stepIconSize: 40,
                    currentStep: _currentStep,
                    physics: const BouncingScrollPhysics(),
                    steps: _buildSteps(),
                    onStepContinue: _onStepContinue,
                    stepIconBuilder: (stepIndex, stepState) => CircleAvatar(
                      backgroundColor: AppTheme.accentColor,
                      child: Text(
                        (stepIndex + 1).toString(),
                        style: const TextStyle(
                            fontFamily: 'Cairo',
                            fontSize: 16,
                            color: Colors.white,
                            fontWeight: FontWeight.w600),
                      ),
                    ),
                    onStepCancel: _onStepCancel,
                    onStepTapped: (index) {
                      if (mounted) {
                        setState(() => _currentStep = index);
                      }
                    },
                    controlsBuilder: (context, details) {
                      return Padding(
                        padding: const EdgeInsets.only(top: 20),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            if (_currentStep > 0)
                              InkWell(
                                onTap: details.onStepCancel,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 32, vertical: 8),
                                  decoration: BoxDecoration(
                                      color: AppTheme.componentBackColor,
                                      borderRadius: BorderRadius.circular(10)),
                                  child: const Text('Back',
                                      style: TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 16,
                                          color: Colors.black,
                                          fontWeight: FontWeight.w600)),
                                ),
                              ),
                            const SizedBox(width: 10),
                            InkWell(
                              onTap: details.onStepContinue,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 32, vertical: 8),
                                decoration: BoxDecoration(
                                  boxShadow: const [
                                    BoxShadow(
                                        blurStyle: BlurStyle.outer,
                                        color: AppTheme.accentColor,
                                        blurRadius: 3)
                                  ],
                                  color: AppTheme.accentColor,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Text(
                                  _currentStep == _buildSteps().length - 1
                                      ? 'Save'
                                      : 'Next',
                                  style: const TextStyle(
                                      fontFamily: 'Cairo',
                                      fontSize: 16,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600),
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                        color: Colors.greenAccent,
                        borderRadius: BorderRadius.circular(8)),
                    child: const Text('How it works?',
                        style: TextStyle(fontFamily: 'Alatsi', fontSize: 20)),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<EnhanceStep> _buildSteps() {
    return [
      EnhanceStep(
        title: const Text('Website URL*'),
        content: Form(
          key: _formKey,
          child: TextFormField(
            controller: _urlController,
            decoration: InputDecoration(
              fillColor: AppTheme.componentBackColor,
              filled: true,
              hintText: 'www.website.com',
              border: OutlineInputBorder(
                  borderSide: BorderSide.none,
                  borderRadius: BorderRadius.circular(10)),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) return 'Required Field';
              if (!RegExp(r'^(www\.)?[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$')
                  .hasMatch(value)) {
                return 'Invalid URL format';
              }
              return null;
            },
          ),
        ),
      ),
      EnhanceStep(
        title: const Text('Website information*'),
        subtitle: const Text('Language, sponsored, Categories, Country'),
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Language*',
                style: TextStyle(fontSize: 16, color: Colors.black)),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                  color: AppTheme.componentBackColor,
                  borderRadius: BorderRadius.circular(10)),
              child: LanguagePickerDropdown(
                initialValue: _selectedLanguage,
                onValuePicked: (Language language) {
                  if (mounted) {
                    setState(() => _selectedLanguage = language);
                  }
                },
              ),
            ),
            const SizedBox(height: 20),
            const Text('Content has to be marked as sponsored *',
                style:
                    TextStyle(fontSize: 16, color: Colors.black, height: 1.5)),
            Text(
                'Disclosure is used to label sponsored content, e.g.: “Promoted by”, “Sponsored by”, “Presented by”, etc.',
                style: TextStyle(fontSize: 14, color: AppTheme.textSecondary)),
            Row(
              children: [
                Radio<bool>(
                  value: true,
                  groupValue: _isSponsored,
                  activeColor: AppTheme.accentColor,
                  onChanged: (value) {
                    if (mounted) {
                      setState(() => _isSponsored = value!);
                    }
                  },
                ),
                const Text('Yes',
                    style: TextStyle(
                        fontSize: 16, color: Colors.black, height: 1.5)),
                const SizedBox(width: 20),
                Radio<bool>(
                  value: false,
                  groupValue: _isSponsored,
                  activeColor: AppTheme.accentColor,
                  onChanged: (value) {
                    if (mounted) {
                      setState(() => _isSponsored = value!);
                    }
                  },
                ),
                const Text('No',
                    style: TextStyle(
                        fontSize: 16, color: Colors.black, height: 1.5)),
              ],
            ),
            const SizedBox(height: 20),
            const Text('Categories*',
                style:
                    TextStyle(fontSize: 16, color: Colors.black, height: 1.5)),
            MultiSelectDialogField(
              items: _categories,
              dialogWidth: 350,
              title: const Text('Website Categories'),
              selectedColor: AppTheme.accentColor,
              decoration: BoxDecoration(
                  color: AppTheme.componentBackColor,
                  borderRadius: BorderRadius.circular(10)),
              buttonText: Text(
                _selectedCategories.isEmpty
                    ? 'Select Categories (up to $_maxCategories)'
                    : 'Selected: ${_selectedCategories.join(', ')}',
                style: const TextStyle(fontSize: 16),
              ),
              buttonIcon: const Icon(Icons.arrow_drop_down),
              searchable: true,
              dialogHeight: 500,
              isDismissible: false,
              validator: (values) {
                if (values == null || values.isEmpty) {
                  return 'Please select at least one category';
                }
                if (values.length > _maxCategories) {
                  return 'You can only select up to $_maxCategories categories';
                }
                return null;
              },
              onConfirm: (List<dynamic> selectedValues) {
                if (mounted) {
                  setState(() {
                    _selectedCategories =
                        selectedValues.length <= _maxCategories
                            ? selectedValues
                            : selectedValues.sublist(0, _maxCategories);
                  });
                }
              },
              initialValue: _selectedCategories,
              chipDisplay: MultiSelectChipDisplay.none(),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Country*',
                    style: TextStyle(
                        fontSize: 16, color: Colors.black, height: 1.5)),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                      color: AppTheme.componentBackColor,
                      borderRadius: BorderRadius.circular(10)),
                  child: TextButton(
                    onPressed: () {
                      showCountryPicker(
                        customFlagBuilder: (country) => Container(
                          margin: const EdgeInsets.only(right: 8),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(4),
                            child: Image.network(
                              'https://flagcdn.com/w40/${country.countryCode.toLowerCase()}.png',
                              width: 30,
                              height: 20,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Text(country.flagEmoji),
                            ),
                          ),
                        ),
                        context: context,
                        showPhoneCode: false,
                        onSelect: (Country country) {
                          if (mounted) {
                            setState(() => _selectedCountry = country);
                          }
                        },
                      );
                    },
                    child: Text(
                      _selectedCountry == null
                          ? 'Select Country'
                          : _selectedCountry!.name,
                      style: const TextStyle(
                          fontSize: 16, color: AppTheme.accentColor),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      EnhanceStep(
        title: const Text('Details*'),
        subtitle:
            const Text('DA, DR, Spam Score, Traffic, Pricing, Content Rules'),
        content: Column(
          children: [
            _buildNumberField(
                'DA (Domain Authority) *',
                _daController,
                '0-100',
                'Moz score (0-100)—highlight your site’s SEO strength and credibility.',
                0,
                100),
            _buildNumberField(
                'DR (Domain Rating) *',
                _drController,
                '0-100',
                'Ahrefs rating (0-100)—show off your backlink power and authority.',
                0,
                100),
            _buildNumberField(
                'Spam Score (%)',
                _spamScoreController,
                '0-100',
                'Spam score percentage (0-100)—lower is better. Indicates site quality and trustworthiness.',
                0,
                100),
            _buildTrafficField(),
            _buildNumberField('Base Pricing *', _pricingController,
                '\$$_defaultPricing', 'Set your base rate.', 0, null,
                isDecimal: true),
            const Divider(),
            _buildTopicsField(
                'Allowed Topics *', _allowedTopicsController, _allowedTopics),
            _buildTopicsField('Disallowed Topics *',
                _disallowedTopicsController, _disallowedTopics),
            Padding(
              padding: const EdgeInsets.only(bottom: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('Charge extra for special topics?',
                      style: TextStyle(fontSize: 16)),
                  Switch(
                    value: _enableSpecialTopicsPricing,
                    activeColor: AppTheme.accentColor,
                    onChanged: (value) {
                      if (mounted) {
                        setState(() => _enableSpecialTopicsPricing = value);
                      }
                    },
                  ),
                ],
              ),
            ),
            if (_enableSpecialTopicsPricing)
              _buildNumberField(
                'Special Topics Extra Price',
                _specialTopicsPriceController,
                '20\$',
                'Additional cost for special topics. Default: $_specialTopicsSurchargePercentage% of base price.',
                0,
                null,
                isDecimal: true,
              ),
            _buildNumberField(
                'Min Word Count *',
                _wordCountMinController,
                _defaultMinWordCount.toString(),
                'Minimum word count for content.',
                100,
                10000),
            _buildNumberField(
                'Max Word Count *',
                _wordCountMaxController,
                _defaultMaxWordCount.toString(),
                'Maximum word count for content.',
                100,
                10000),
            _buildNumberField(
                'Max Links *',
                _maxLinksController,
                _defaultMaxLinks.toString(),
                'Maximum number of links allowed in content.',
                0,
                10),
            Padding(
              padding: const EdgeInsets.only(bottom: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Backlink Type *',
                          style: TextStyle(fontSize: 16)),
                      Text('Dofollow links boost SEO; nofollow don’t.',
                          style: TextStyle(
                              fontSize: 14, color: AppTheme.textSecondary)),
                    ],
                  ),
                  DropdownButton<String>(
                    value: _backlinkType,
                    items: const [
                      DropdownMenuItem(
                          value: 'dofollow', child: Text('Dofollow')),
                      DropdownMenuItem(
                          value: 'nofollow', child: Text('Nofollow')),
                    ],
                    onChanged: (value) {
                      if (mounted) {
                        setState(() => _backlinkType = value!);
                      }
                    },
                  ),
                ],
              ),
            ),
            if (_backlinkType == 'dofollow')
              _buildNumberField(
                'Dofollow Cost (\$)',
                _dofollowCostController,
                '0',
                'Set a specific cost for dofollow backlinks (overrides percentage calculation).',
                0,
                null,
                isDecimal: true,
              ),
          ],
        ),
      ),
    ];
  }

  Widget _buildNumberField(String label, TextEditingController controller,
      String hint, String description, int min, int? max,
      {bool isDecimal = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(label, style: const TextStyle(fontSize: 16)),
              Text(description,
                  style:
                      TextStyle(fontSize: 14, color: AppTheme.textSecondary)),
            ],
          ),
          Row(
            children: [
              SizedBox(
                width: 100,
                child: TextFormField(
                  controller: controller,
                  keyboardType: isDecimal
                      ? const TextInputType.numberWithOptions(decimal: true)
                      : TextInputType.number,
                  textAlign: TextAlign.center,
                  decoration: InputDecoration(
                    fillColor: AppTheme.componentBackColor,
                    filled: true,
                    hintText: hint,
                    hintStyle: TextStyle(color: AppTheme.textSecondary),
                    border: OutlineInputBorder(
                        borderSide: BorderSide.none,
                        borderRadius: BorderRadius.circular(10)),
                  ),
                  validator: (value) {
                    // Skip validation if field is empty and not required (doesn't have * in label)
                    if (value == null || value.isEmpty) {
                      return label.contains('*') ? 'Required' : null;
                    }

                    final numValue = isDecimal
                        ? double.tryParse(value)
                        : int.tryParse(value);
                    if (numValue == null) return 'Invalid number';
                    if (numValue < min) return 'Minimum $min';
                    if (max != null && numValue > max) return 'Maximum $max';
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 5),
              label == 'Base Pricing *' ||
                      label == 'Special Topics Extra Price' ||
                      label == 'Dofollow Cost (\$)'
                  ? Text('\$',
                      style: TextStyle(
                          fontSize: 16, color: AppTheme.textSecondary))
                  : label == 'Spam Score (%)'
                      ? Text('%',
                          style: TextStyle(
                              fontSize: 16, color: AppTheme.textSecondary))
                      : const SizedBox.shrink(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTrafficField() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Traffic *', style: TextStyle(fontSize: 16)),
              Text(
                  'Monthly visitors (e.g., 20K or 1.5M)—prove your site’s reach.',
                  style:
                      TextStyle(fontSize: 14, color: AppTheme.textSecondary)),
            ],
          ),
          SizedBox(
            width: 100,
            child: TextFormField(
              controller: _trafficController,
              textAlign: TextAlign.center,
              decoration: InputDecoration(
                fillColor: AppTheme.componentBackColor,
                filled: true,
                hintText: _defaultTraffic,
                hintStyle: TextStyle(color: AppTheme.textSecondary),
                border: OutlineInputBorder(
                    borderSide: BorderSide.none,
                    borderRadius: BorderRadius.circular(10)),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) return 'Required';
                if (!RegExp(r'^\d+(\.\d+)?[kKmM]?$').hasMatch(value)) {
                  return 'Use format: 20K or 1.5M';
                }
                return null;
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopicsField(
      String label, TextEditingController controller, List<String> topics) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: const TextStyle(fontSize: 16)),
          TextFormField(
            controller: controller,
            decoration: InputDecoration(
              fillColor: AppTheme.componentBackColor,
              filled: true,
              hintText: 'Enter topics (comma-separated)',
              border: OutlineInputBorder(
                  borderSide: BorderSide.none,
                  borderRadius: BorderRadius.circular(10)),
            ),
            onFieldSubmitted: (value) {
              if (mounted) {
                setState(() {
                  topics.clear();
                  topics.addAll(value
                      .split(',')
                      .map((e) => e.trim())
                      .where((e) => e.isNotEmpty));
                  controller.clear();
                });
              }
            },
            validator: (value) {
              if (topics.isEmpty && (value == null || value.isEmpty)) {
                return 'Required';
              }
              return null;
            },
          ),
          if (topics.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 5),
              child: Text('Selected: ${topics.join(', ')}',
                  style: const TextStyle(fontSize: 14)),
            ),
        ],
      ),
    );
  }
}
