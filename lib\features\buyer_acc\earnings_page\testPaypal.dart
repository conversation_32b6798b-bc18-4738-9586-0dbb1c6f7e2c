// import 'dart:html' as html;
// import 'dart:js' as js;
// import 'package:flutter/material.dart';
// import 'dart:ui_web' as ui_web;

// class PayPalDialogWidget extends StatefulWidget {
//   final double amount;
//   final String currency;
//   final Function(String orderId) onPaymentSuccess;
//   final Function(String error) onPaymentError;
//   final Function() onPaymentCancelled;

//   const PayPalDialogWidget({
//     Key? key,
//     required this.amount,
//     this.currency = 'USD',
//     required this.onPaymentSuccess,
//     required this.onPaymentError,
//     required this.onPaymentCancelled,
//   }) : super(key: key);

//   @override
//   _PayPalDialogWidgetState createState() => _PayPalDialogWidgetState();
// }

// class _PayPalDialogWidgetState extends State<PayPalDialogWidget> {
//   late html.DivElement _paypalButtonContainer;
//   bool _isLoaded = false;
//   bool _isProcessing = false;
//   final String containerId =
//       'paypal-button-container-${DateTime.now().millisecondsSinceEpoch}';

//   @override
//   void initState() {
//     super.initState();
//     _initPayPalScript();
//   }

//   void _initPayPalScript() {
//     // Create a container for the PayPal button
//     _paypalButtonContainer = html.DivElement()
//       ..id = containerId
//       ..style.height = '150px'
//       ..style.width = '100%';

//     html.document.body?.append(_paypalButtonContainer);

//     // Register view
//     ui_web.platformViewRegistry.registerViewFactory(
//       containerId,
//       (int viewId) => _paypalButtonContainer,
//     );

//     // Load PayPal SDK script
//     final script = html.ScriptElement()
//       ..src =
//           'https://www.paypal.com/sdk/js?client-id=Ad5SS6XP1eziFBc26-_PYEn9KBEhCDtHJd-CtNoVTiSvhIoXGrhZ7ZBN9YGvTXKhrxATLKte7swmW2DI&currency=${widget.currency}'
//       ..async = true;

//     script.onLoad.listen((_) {
//       setState(() {
//         _isLoaded = true;
//       });
//       _setupPayPalButtons();
//     });

//     html.document.head?.append(script);
//   }

//   void _setupPayPalButtons() {
//     try {
//       // Correctly access the Buttons constructor
//       final options = js.JsObject.jsify({
//         'createOrder': (data, actions) {
//           setState(() {
//             _isProcessing = true;
//           });

//           // For real implementation, you would use actions.order.create()
//           return js.context['Promise'].callMethod('resolve',
//               ['MOCK_ORDER_ID_${DateTime.now().millisecondsSinceEpoch}']);
//         },
//         'onApprove': (data, actions) {
//           final orderID = data['orderID'] ?? 'unknown';
//           print('Payment approved! Order ID: $orderID');

//           setState(() {
//             _isProcessing = false;
//           });

//           Navigator.of(context).pop();
//           widget.onPaymentSuccess(orderID);
//         },
//         'onCancel': (data, actions) {
//           print('Payment cancelled');

//           setState(() {
//             _isProcessing = false;
//           });

//           Navigator.of(context).pop();
//           widget.onPaymentCancelled();
//         },
//         'onError': (error) {
//           print('Payment error: $error');

//           setState(() {
//             _isProcessing = false;
//           });

//           Navigator.of(context).pop();
//           widget.onPaymentError(error.toString());
//         }
//       });

//       // Call the Buttons constructor directly and then render
//       final paypalButtons =
//           js.JsObject(js.context['paypal']['Buttons'], [options]);
//       paypalButtons.callMethod('render', ['#$containerId']);

//       // Make the container visible
//       _paypalButtonContainer.style.display = 'block';
//     } catch (e) {
//       print('Error setting up PayPal buttons: $e');
//       widget.onPaymentError('Failed to initialize PayPal: $e');
//     }
//   }

//   @override
//   void dispose() {
//     // Clean up the PayPal button container
//     _paypalButtonContainer.remove();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Dialog(
//       child: Container(
//         padding: const EdgeInsets.all(20),
//         width: 400,
//         height: 300,
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             const Text(
//               'PayPal Checkout',
//               style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
//             ),
//             const SizedBox(height: 20),
//             Text(
//               'Amount: ${widget.currency} ${widget.amount.toStringAsFixed(2)}',
//               style: const TextStyle(fontSize: 16),
//             ),
//             const SizedBox(height: 20),
//             if (!_isLoaded)
//               const CircularProgressIndicator()
//             else
//               SizedBox(
//                 height: 150,
//                 child: HtmlElementView(
//                   viewType: containerId,
//                 ),
//               ),
//             if (_isProcessing)
//               const Padding(
//                 padding: EdgeInsets.only(top: 10),
//                 child: Text('Processing payment...'),
//               ),
//           ],
//         ),
//       ),
//     );
//   }
// }

// // Usage example:
// class PayPalCheckoutPage extends StatelessWidget {
//   const PayPalCheckoutPage({Key? key}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('PayPal Checkout Demo'),
//       ),
//       body: Center(
//         child: ElevatedButton(
//           child: const Text('Pay with PayPal'),
//           onPressed: () {
//             showDialog(
//               context: context,
//               builder: (BuildContext context) {
//                 return PayPalDialogWidget(
//                   amount: 25.99,
//                   currency: 'USD',
//                   onPaymentSuccess: (String orderId) {
//                     print('Payment successful! Order ID: $orderId');
//                     ScaffoldMessenger.of(context).showSnackBar(
//                       SnackBar(
//                           content:
//                               Text('Payment successful! Order ID: $orderId')),
//                     );
//                   },
//                   onPaymentError: (String error) {
//                     print('Payment error: $error');
//                     ScaffoldMessenger.of(context).showSnackBar(
//                       SnackBar(content: Text('Payment error: $error')),
//                     );
//                   },
//                   onPaymentCancelled: () {
//                     print('Payment cancelled');
//                     ScaffoldMessenger.of(context).showSnackBar(
//                       const SnackBar(content: Text('Payment cancelled')),
//                     );
//                   },
//                 );
//               },
//             );
//           },
//         ),
//       ),
//     );
//   }
// }

import 'dart:html' as html;
import 'dart:js' as js;
import 'package:flutter/material.dart';
import 'dart:ui_web' as ui_web;

class PayPalDialogWidget extends StatefulWidget {
  final double amount;
  final String currency;
  final Function(String orderId) onPaymentSuccess;
  final Function(String error) onPaymentError;
  final Function() onPaymentCancelled;

  const PayPalDialogWidget({
    Key? key,
    required this.amount,
    this.currency = 'USD',
    required this.onPaymentSuccess,
    required this.onPaymentError,
    required this.onPaymentCancelled,
  }) : super(key: key);

  @override
  _PayPalDialogWidgetState createState() => _PayPalDialogWidgetState();
}

class _PayPalDialogWidgetState extends State<PayPalDialogWidget> {
  late html.DivElement _paypalButtonContainer;
  bool _isLoaded = false;
  bool _isProcessing = false;
  final String containerId =
      'paypal-sandbox-container-${DateTime.now().millisecondsSinceEpoch}';

  // Sandbox client ID - replace with your own sandbox client ID from developer.paypal.com
  final String sandboxClientId =
      'Ad5SS6XP1eziFBc26-_PYEn9KBEhCDtHJd-CtNoVTiSvhIoXGrhZ7ZBN9YGvTXKhrxATLKte7swmW2DI';

  @override
  void initState() {
    super.initState();
    _initPayPalScript();
  }

  void _initPayPalScript() {
    // Create a container for the PayPal button
    _paypalButtonContainer = html.DivElement()
      ..id = containerId
      ..style.height = '150px'
      ..style.width = '100%';

    html.document.body?.append(_paypalButtonContainer);

    // Register view
    ui_web.platformViewRegistry.registerViewFactory(
      containerId,
      (int viewId) => _paypalButtonContainer,
    );

    // Load PayPal SDK script with sandbox environment
    final script = html.ScriptElement()
      ..src =
          'https://www.paypal.com/sdk/js?client-id=$sandboxClientId&currency=${widget.currency}&intent=authorize&debug=true'
      ..async = true;

    script.onLoad.listen((_) {
      setState(() {
        _isLoaded = true;
      });
      _setupPayPalButtons();
    });

    // final isScriptAlreadyAdded = html.document.querySelectorAll('script').any(
    //     (script) =>
    //         script is html.ScriptElement &&
    //         script.src.contains('paypal.com/sdk/js'));

    html.document.head?.append(script);
  }

  void _setupPayPalButtons() {
    try {
      // Create options for sandbox testing
      final options = js.JsObject.jsify({
        'createOrder': (data, actions) {
          setState(() {
            _isProcessing = true;
          });

          // Use the actions.order.create method for sandbox testing
          return actions['order'].callMethod('create', [
            js.JsObject.jsify({
              'purchase_units': [
                {
                  'amount': {
                    'value': widget.amount.toStringAsFixed(2),
                    'currency_code': widget.currency
                  }
                }
              ]
            })
          ]);
        },
        'onApprove': (data, actions) {
          print('Payment approved! Capturing order...');
          // actions['order'].callMethod('capture').callMethod('then', [
          //   (details) {
          //     // final orderId = details['id'] ?? data['orderID'];
          //     // print('Order captured: $orderId');
          //     // Navigator.of(context).pop();
          //     // widget.onPaymentSuccess(orderId);
          //   }
          final orderId = data['orderID'];
          print('Order captured: $orderId');
          Navigator.of(context).pop();
          widget.onPaymentSuccess(orderId);
        },
        'onCancel': (data, actions) {
          print('Payment cancelled');

          setState(() {
            _isProcessing = false;
          });

          Navigator.of(context).pop();
          widget.onPaymentCancelled();
        },
        'onError': (error) {
          print('Payment error: $error');

          setState(() {
            _isProcessing = false;
          });

          Navigator.of(context).pop();
          widget.onPaymentError(error.toString());
        },
        // Use sandbox styling
        'style': {
          'layout': 'vertical',
          'color': 'gold',
          'shape': 'rect',
          'label': 'paypal'
        }
      });

      // Create and render the PayPal Buttons
      final paypalButtons =
          js.JsObject(js.context['paypal']['Buttons'], [options]);
      paypalButtons.callMethod('render', ['#$containerId']);

      // Make the container visible
      _paypalButtonContainer.style.display = 'block';
    } catch (e) {
      print('Error setting up PayPal buttons: $e');
      widget.onPaymentError('Failed to initialize PayPal: $e');
    }
  }

  @override
  void dispose() {
    // Clean up the PayPal button container
    _paypalButtonContainer.remove();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: Colors.white,
        ),
        padding: const EdgeInsets.all(20),
        width: 400,
        height: 500,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'PayPal Sandbox Checkout',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            Text(
              'Test Amount: ${widget.currency} ${widget.amount.toStringAsFixed(2)}',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 5),
            const Text(
              '(Sandbox Mode - No real money will be charged)',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
            const SizedBox(height: 20),
            if (!_isLoaded)
              const CircularProgressIndicator()
            else
              SizedBox(
                height: 300,
                child: HtmlElementView(
                  viewType: containerId,
                ),
              ),
            if (_isProcessing)
              const Padding(
                padding: EdgeInsets.only(top: 10),
                child: Text('Processing payment...'),
              ),
          ],
        ),
      ),
    );
  }
}

// Usage example:
class PayPalSandboxPage extends StatelessWidget {
  const PayPalSandboxPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PayPal Sandbox Demo'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'PayPal Sandbox Testing',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            const Text(
              'Use these sandbox credentials to test:\n'
              'Email: <EMAIL>\n'
              'Password: Z>5(7rBq',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              child: const Text('Pay with PayPal (Sandbox)'),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return PayPalDialogWidget(
                      amount: 25.99,
                      currency: 'USD',
                      onPaymentSuccess: (String orderId) {
                        print('Payment successful! Order ID: $orderId');
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                              content: Text(
                                  'Sandbox payment successful! Order ID: $orderId')),
                        );
                      },
                      onPaymentError: (String error) {
                        print('Payment error: $error');
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                              content: Text('Sandbox payment error: $error')),
                        );
                      },
                      onPaymentCancelled: () {
                        print('Payment cancelled');
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                              content: Text('Sandbox payment cancelled')),
                        );
                      },
                    );
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

// Don't forget to call this in your main.dart
// void initializePayPalSandbox() {
//   // Any additional initialization code can go here
// }

// import 'dart:ui' as ui;
// import 'dart:html' as html;
// import 'dart:js' as js;
// import 'package:flutter/material.dart';

// class PayPalWebView extends StatefulWidget {
//   final double amount;
//   final String currency;
//   final Function(String orderId) onPaymentSuccess;
//   final Function() onPaymentCancelled;
//   final Function(String error) onPaymentError;

//   const PayPalWebView({
//     Key? key,
//     required this.amount,
//     required this.currency,
//     required this.onPaymentSuccess,
//     required this.onPaymentCancelled,
//     required this.onPaymentError,
//   }) : super(key: key);

//   @override
//   State<PayPalWebView> createState() => _PayPalWebViewState();
// }

// class _PayPalWebViewState extends State<PayPalWebView> {
//   late html.DivElement _paypalButtonContainer;
//   bool _isProcessing = false;
//   final String containerId =
//       'paypal-button-container-${DateTime.now().millisecondsSinceEpoch}';

//   @override
//   void initState() {
//     super.initState();
//     _initializePayPal();
//   }

//   void _initializePayPal() {
//     _paypalButtonContainer = html.DivElement()
//       ..id = containerId
//       ..style.width = '100%';

//     // Register the container so Flutter can render it
//     // ignore: undefined_prefixed_name
//     ui.platformViewRegistry.registerViewFactory(
//         containerId, (int viewId) => _paypalButtonContainer);

//     // Load PayPal SDK script
//     final html.ScriptElement script = html.ScriptElement()
//       ..type = 'text/javascript'
//       ..src =
//           'https://www.paypal.com/sdk/js?client-id=Ad5SS6XP1eziFBc26-_PYEn9KBEhCDtHJd-CtNoVTiSvhIoXGrhZ7ZBN9YGvTXKhrxATLKte7swmW2DI&currency=${widget.currency}&intent=authorize&debug=true'
//       ..async = true;

//     script.onLoad.listen((event) {
//       _setupPayPalButtons();
//     });

//     script.onError.listen((error) {
//       widget.onPaymentError("Failed to load PayPal script.");
//     });

//     html.document.body!.append(script);
//   }

//   void _setupPayPalButtons() {
//     try {
//       final options = js.JsObject.jsify({
//         'createOrder': (data, actions) {
//           setState(() {
//             _isProcessing = true;
//           });

//           return actions['order'].callMethod('create', [
//             js.JsObject.jsify({
//               'purchase_units': [
//                 {
//                   'amount': {
//                     'value': widget.amount.toStringAsFixed(2),
//                     'currency_code': widget.currency,
//                   }
//                 }
//               ]
//             })
//           ]);
//         },
//         'onApprove': (data, actions) {
//           final orderID = data['orderID'];
//           print('Order created. Order ID: $orderID');

//           setState(() {
//             _isProcessing = false;
//           });

//           Navigator.of(context).pop();
//           widget.onPaymentSuccess(orderID);
//         },
//         'onCancel': (data, actions) {
//           print('Payment cancelled');

//           setState(() {
//             _isProcessing = false;
//           });

//           Navigator.of(context).pop();
//           widget.onPaymentCancelled();
//         },
//         'onError': (error) {
//           print('Payment error: $error');

//           setState(() {
//             _isProcessing = false;
//           });

//           Navigator.of(context).pop();
//           widget.onPaymentError(error.toString());
//         },
//         'style': {
//           'layout': 'vertical',
//           'color': 'gold',
//           'shape': 'rect',
//           'label': 'paypal'
//         }
//       });

//       final paypalButtons =
//           js.JsObject(js.context['paypal']['Buttons'], [options]);
//       paypalButtons.callMethod('render', ['#$containerId']);

//       _paypalButtonContainer.style.display = 'block';
//     } catch (e) {
//       print('Error setting up PayPal buttons: $e');
//       widget.onPaymentError('Failed to initialize PayPal: $e');
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Dialog(
//       child: Stack(
//         children: [
//           SizedBox(
//             height: 400,
//             width: 400,
//             child: HtmlElementView(viewType: containerId),
//           ),
//           if (_isProcessing)
//             Positioned.fill(
//               child: Container(
//                 color: Colors.black45,
//                 child: const Center(
//                   child: CircularProgressIndicator(),
//                 ),
//               ),
//             ),
//         ],
//       ),
//     );
//   }
// }
