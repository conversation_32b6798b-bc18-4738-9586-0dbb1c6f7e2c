import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:guest_posts/core/routes/app_routes.dart';
import 'package:guest_posts/core/services/auth_service.dart';
import 'package:guest_posts/features/auth/login_screen.dart';
import 'package:guest_posts/features/auth/register_screen.dart';
import 'package:guest_posts/features/landing/landing_page.dart';
import 'package:guest_posts/home_screen.dart';

class AppRouter {
  static final AuthService _authService = AuthService();

  static final GoRouter router = GoRouter(
    initialLocation: AppRoutes.landing,
    debugLogDiagnostics: true,
    redirect: (context, state) async {
      final isAuthenticated = _authService.isLoggedIn;
      final currentRoute = state.uri.toString();

      // Handle authentication redirects
      if (isAuthenticated && AppRoutes.isPublicRoute(currentRoute)) {
        return AppRoutes.dashboard;
      }
      if (!isAuthenticated && AppRoutes.isProtectedRoute(currentRoute)) {
        return AppRoutes.landing;
      }

      // Handle role-based access for authenticated users
      if (isAuthenticated) {
        final isPublisher = await _authService.isCurrentUserPublisher();

        if (AppRoutes.isPublisherRoute(currentRoute) && !isPublisher) {
          return AppRoutes.dashboard;
        }
      }

      return null;
    },
    routes: [
      GoRoute(
        path: '/',
        redirect: (context, state) => AppRoutes.landing,
      ),
      GoRoute(
        path: AppRoutes.landing,
        builder: (context, state) => const LandingPage(),
      ),
      GoRoute(
        path: AppRoutes.auth,
        builder: (context, state) => const LoginScreen(),
        routes: [
          GoRoute(
            path: 'register',
            builder: (context, state) => SignUpScreen(
              authService: _authService,
            ),
          ),
        ],
      ),
      // Parameterized routes
      GoRoute(
        path: AppRoutes.listingDetails,
        builder: (context, state) {
          final params =
              AppRoutes.extractParams(AppRoutes.listingDetails, state.uri.path);
          final queryParams = AppRoutes.parseQueryString(state.uri.query);
          return HomeScreen(
            initialPage: 'listings',
            queryParams: {
              'id': params['id'],
              ...queryParams,
            },
          );
        },
      ),
      GoRoute(
        path: AppRoutes.orderDetails,
        builder: (context, state) {
          final params =
              AppRoutes.extractParams(AppRoutes.orderDetails, state.uri.path);
          final queryParams = AppRoutes.parseQueryString(state.uri.query);
          return HomeScreen(
            initialPage: 'orders',
            queryParams: {
              'id': params['id'],
              ...queryParams,
            },
          );
        },
      ),
      GoRoute(
        path: AppRoutes.userProfile,
        builder: (context, state) {
          final params =
              AppRoutes.extractParams(AppRoutes.userProfile, state.uri.path);
          final queryParams = AppRoutes.parseQueryString(state.uri.query);
          return HomeScreen(
            initialPage: 'profile',
            queryParams: {
              'userId': params['userId'],
              ...queryParams,
            },
          );
        },
      ),
      GoRoute(
        path: AppRoutes.editListingWithId,
        builder: (context, state) {
          final params = AppRoutes.extractParams(
              AppRoutes.editListingWithId, state.uri.path);
          print('params: $params');
          return HomeScreen(
            initialPage: 'edit-listing',
            queryParams: {
              'id': params['id'],
            },
          );
        },
      ),
      // Dynamic page route for other pages
      GoRoute(
        path: '/:page',
        builder: (context, state) {
          final page = AppRoutes.getPageName(
              state.pathParameters['page'] ?? AppRoutes.dashboard);
          final queryParams = AppRoutes.parseQueryString(state.uri.query);
          return HomeScreen(
            initialPage: page,
            queryParams: queryParams,
          );
        },
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 60,
            ),
            const SizedBox(height: 16),
            Text(
              'Route not found: ${state.uri}',
              style: const TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.dashboard),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ),
    ),
  );
}
