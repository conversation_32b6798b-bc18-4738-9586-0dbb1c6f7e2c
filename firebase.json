{"flutter": {"platforms": {"android": {"default": {"projectId": "guestposts-84915", "appId": "1:180868556261:android:771c5e03bacd82dc84bb0c", "fileOutput": "android/app/google-services.json"}}, "dart": {"lib/firebase_options.dart": {"projectId": "guestposts-84915", "configurations": {"android": "1:180868556261:android:771c5e03bacd82dc84bb0c", "ios": "1:180868556261:ios:200558341244b29d84bb0c", "web": "1:180868556261:web:54758fe0ff94f9e384bb0c"}}}}}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint"]}], "hosting": {"site": "guestposts-84915", "public": "build/web/", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]}}