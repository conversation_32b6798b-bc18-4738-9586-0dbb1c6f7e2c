import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:guest_posts/core/services/auth_service.dart';
import 'package:guest_posts/core/utils/colors.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:guest_posts/core/utils/toast_helper.dart';
import 'package:guest_posts/core/theme/app_theme.dart';

class PublisherSupportPage extends StatefulWidget {
  const PublisherSupportPage({super.key});

  @override
  State<PublisherSupportPage> createState() => _PublisherSupportPageState();
}

class _PublisherSupportPageState extends State<PublisherSupportPage> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _requestController = TextEditingController();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AuthService _authService = AuthService();

  var uid = '';
  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _requestController.dispose();

    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _loadUserData();

    // if (_auth.currentUser != null) {
    //   _nameController.text = _auth.currentUser!.displayName ?? '';
    //   _emailController.text = _auth.currentUser!.email ?? '';
    //   uid = _auth.currentUser!.uid;
    // }
  }

  Future<void> _loadUserData() async {
    if (_auth.currentUser != null) {
      final userData = await _authService.getUserData();
      if (userData != null && mounted) {
        setState(() {
          _nameController.text = userData['name'] ?? userData['name'] ?? '';
          _emailController.text =
              userData['email'] ?? _auth.currentUser!.email ?? '';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppTheme.backgroundColor,
      padding: const EdgeInsets.all(16.0),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        // Header Section
        const Text(
          'Contact Support',
          style: TextStyle(
            fontFamily: 'Space',
            fontSize: 30,
            fontWeight: FontWeight.w700,
          ),
        ),
        Divider(
          endIndent: 100,
          color: AppTheme.accentColor,
          thickness: 2,
        ),
        const SizedBox(height: 10),

        // How it Works Section
        Expanded(
            flex: 2,
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text(
                'Please fill out the form below to submit a support request. '
                'Our team will get back to you within 24-48 hours.',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 16,
                  color: const Color.fromARGB(255, 61, 61, 61),
                ),
              ),
              const SizedBox(height: 16),

              // Support Form
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppTheme.accentColor, width: 2),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Your Name',
                      style: TextStyle(
                          fontFamily: 'Cairo',
                          fontWeight: FontWeight.w600,
                          fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _nameController,
                      decoration: InputDecoration(
                        hintText: 'Enter your name',
                        border: OutlineInputBorder(
                          borderSide: BorderSide.none,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        hintStyle: TextStyle(color: AppTheme.textSecondary),
                        filled: true,
                        fillColor: AppTheme.componentBackColor,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Email Field
                    const Text(
                      'Email Address',
                      style: TextStyle(
                          fontFamily: 'Cairo',
                          fontWeight: FontWeight.w600,
                          fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _emailController,
                      decoration: InputDecoration(
                        hintText: 'Enter your email',
                        border: OutlineInputBorder(
                          borderSide: BorderSide.none,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        hintStyle: TextStyle(color: AppTheme.textSecondary),
                        filled: true,
                        fillColor: AppTheme.componentBackColor,
                      ),
                      keyboardType: TextInputType.emailAddress,
                    ),
                    const SizedBox(height: 16),

                    // Support Request Field
                    const Text(
                      'Support Request',
                      style: TextStyle(
                          fontFamily: 'Cairo',
                          fontWeight: FontWeight.w600,
                          fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _requestController,
                      decoration: InputDecoration(
                        hintText: 'Describe your issue or question',
                        border: OutlineInputBorder(
                          borderSide: BorderSide.none,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        hintStyle: TextStyle(color: AppTheme.textSecondary),
                        filled: true,
                        fillColor: AppTheme.componentBackColor,
                      ),
                      maxLines: 5,
                    ),
                    const SizedBox(height: 40),

                    // Submit Button
                    ElevatedButton(
                      onPressed: () async {
                        final name = _nameController.text.trim();
                        final email = _emailController.text.trim();
                        final request = _requestController.text.trim();

                        if (name.isNotEmpty &&
                            email.isNotEmpty &&
                            request.isNotEmpty) {
                          try {
                            await _firestore
                                .collection('support_requests')
                                .add({
                              'name': name,
                              'email': email,
                              'request': request,
                              'uid': uid,
                              'timestamp': FieldValue.serverTimestamp(),
                              'reply': null,
                            });
                            ToastHelper.showSuccess(
                                'Your request has been submitted. We will get back to you soon.');
                            _nameController.clear();
                            _emailController.clear();
                            _requestController.clear();
                          } catch (e) {
                            ToastHelper.showError(
                                'Error submitting request: $e');
                          }
                        } else {
                          ToastHelper.showError('Please fill out all fields.');
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.accentColor,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 32, vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'Submit Request',
                        style: TextStyle(
                          fontFamily: 'Alatsi',
                          fontSize: 16,
                          color: Color.fromARGB(255, 255, 255, 255),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),

              // Additional Contact Info
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color.fromARGB(255, 255, 255, 255),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Additional Contact Methods',
                      style: TextStyle(
                        fontFamily: 'Space',
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Email: <EMAIL>\n'
                      'Phone: +****************\n'
                      'Hours: Mon-Fri, 9AM-5PM EST',
                      style: TextStyle(
                        fontFamily: 'Alatsi',
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ])),
        const SizedBox(height: 10),
      ]),
    );
  }
}
