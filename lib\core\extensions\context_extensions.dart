import 'package:flutter/material.dart';
import 'package:guest_posts/core/utils/toast_helper.dart';

extension ContextExtensions on BuildContext {
  // Replace showSnackBar with toast
  void showToast(String message, {bool isSuccess = true}) {
    ToastHelper.showCustom(message, isSuccess: isSuccess);
  }
  
  void showSuccessToast(String message) {
    ToastHelper.showSuccess(message);
  }
  
  void showErrorToast(String message) {
    ToastHelper.showError(message);
  }
  
  void showInfoToast(String message) {
    ToastHelper.showInfo(message);
  }
  
  void showWarningToast(String message) {
    ToastHelper.showWarning(message);
  }
}