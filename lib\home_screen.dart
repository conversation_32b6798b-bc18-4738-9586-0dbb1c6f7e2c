import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:easy_sidemenu/easy_sidemenu.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts/core/services/auth_service.dart';
import 'package:guest_posts/core/utils/colors.dart';
import 'package:guest_posts/core/utils/toast_helper.dart';
import 'package:guest_posts/dd.dart';
import 'package:guest_posts/features/auth/profile_page.dart';
import 'package:guest_posts/features/buyer_acc/earnings_page/wallet_page.dart';
import 'package:guest_posts/features/buyer_acc/find_publisher.dart';
import 'package:guest_posts/features/buyer_acc/buyer_dashboard.dart';
import 'package:guest_posts/features/buyer_acc/orders_page/orders_page.dart';
import 'package:guest_posts/features/buyer_acc/invoices_page/invoices_page.dart';
import 'package:guest_posts/features/chat/chat_dialog.dart';
import 'package:guest_posts/features/publisher_acc/home/<USER>';
import 'package:guest_posts/features/publisher_acc/home/<USER>/earnings_page.dart';
import 'package:guest_posts/features/publisher_acc/home/<USER>/add_website.dart';
import 'package:guest_posts/features/publisher_acc/home/<USER>';
import 'package:guest_posts/features/publisher_acc/home/<USER>/publisher_orders_page.dart';
import 'package:guest_posts/features/publisher_acc/home/<USER>/publisher_invoices_page.dart';
import 'package:guest_posts/features/support_page/support_page.dart';
import 'package:guest_posts/core/routes/app_routes.dart';
import 'package:intl/intl.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:guest_posts/core/theme/app_theme.dart';

class HomeScreen extends StatefulWidget {
  final String initialPage;
  final Map<String, dynamic> queryParams;

  const HomeScreen({
    super.key,
    this.initialPage = 'dashboard',
    required this.queryParams,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final SideMenuController sideMenu = SideMenuController();
  bool _sideToggle = false;
  final AuthService _authService = AuthService();
  String pageTitle = 'Dashboard';

  final Map<String, int> _pageToIndex = {
    AppRoutes.getPageName(AppRoutes.dashboard): 0,
    AppRoutes.getPageName(AppRoutes.listings): 1,
    AppRoutes.getPageName(AppRoutes.orders): 2,
    AppRoutes.getPageName(AppRoutes.earnings): 3,
    AppRoutes.getPageName(AppRoutes.invoices): 4,
    AppRoutes.getPageName(AppRoutes.support): 5,
    AppRoutes.getPageName(AppRoutes.addListing): 6,
    AppRoutes.getPageName(AppRoutes.profile): 7,
    AppRoutes.getPageName(AppRoutes.buy_post_buyer): 8,
    AppRoutes.getPageName(AppRoutes.dashboard_buyer): 0,
    AppRoutes.getPageName(AppRoutes.listings_buyer): 1,
    AppRoutes.getPageName(AppRoutes.orders_buyer): 2,
    AppRoutes.getPageName(AppRoutes.earnings_buyer): 3,
    AppRoutes.getPageName(AppRoutes.invoices_buyer): 4,
    AppRoutes.getPageName(AppRoutes.support_buyer): 5,
    AppRoutes.getPageName(AppRoutes.profile_buyer): 6,
  };
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Future<void> _changeRole() async {
    final userData = await _authService.getUserData();
    try {
      await _authService.updateUserProfile(
        isPublisher: !userData?['isPublisher'],
      );

      ToastHelper.showSuccess('Role updated successfully');
    } catch (e) {
      ToastHelper.showError('Failed to update role: $e');
    }
  }

  @override
  void initState() {
    super.initState();
    final initialIndex = _pageToIndex[widget.initialPage] ?? 0;
    sideMenu.changePage(initialIndex);
    // Initialize pageTitle based on initialPage
    pageTitle = widget.initialPage
        .split('-')
        .map((word) =>
            word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '')
        .join(' ');
  }

  @override
  void dispose() {
    sideMenu.dispose();
    super.dispose();
  }

  void _navigateToPage(String page, {Map<String, dynamic>? queryParams}) {
    final queryString = AppRoutes.buildQueryString(queryParams);
    context.go('/$page$queryString');
  }

  void _showChatDialog(BuildContext context, Map<String, dynamic> userData) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          width: 400,
          height: 600,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Header
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(20)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Your Chats',
                      style: TextStyle(
                        fontFamily: 'Alatsi',
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.black54),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
              // Chat List
              Expanded(
                child: StreamBuilder<QuerySnapshot>(
                  stream: _firestore
                      .collection('chats')
                      .where('participants', arrayContains: userData['uid'])
                      .orderBy('lastMessageTime', descending: true)
                      .snapshots(),
                  builder: (context, snapshot) {
                    if (snapshot.hasError) {
                      return Center(child: Text('Error: ${snapshot.error}'));
                    }

                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    final chats = snapshot.data?.docs ?? [];

                    if (chats.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.chat_bubble_outline,
                              size: 64,
                              color: Colors.grey[300],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No chats available',
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                color: AppTheme.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    return ListView.separated(
                      padding: const EdgeInsets.all(16),
                      itemCount: chats.length,
                      separatorBuilder: (context, index) =>
                          const Divider(height: 1),
                      itemBuilder: (context, index) {
                        final chat = chats[index];
                        final chatData = chat.data() as Map<String, dynamic>;
                        final List<dynamic> unreadFor =
                            chatData['unreadFor'] ?? [];
                        final bool hasUnread =
                            unreadFor.contains(userData['uid']);
                        final String orderId = chatData['orderId'] ?? '';
                        final String orderTitle =
                            chatData['orderTitle'] ?? 'Chat';
                        final List<dynamic> participants =
                            chatData['participants'] ?? [];

                        // Determine the other participant
                        final String otherParticipantId =
                            participants.firstWhere(
                          (id) => id != userData['uid'],
                          orElse: () => 'Unknown',
                        );

                        // Get the role of the other participant
                        final String otherRole = userData['isPublisher'] == true
                            ? 'Buyer'
                            : 'Publisher';

                        // Get the last message and time
                        final String lastMessage =
                            chatData['lastMessage'] ?? 'No messages yet';
                        final Timestamp? lastMessageTime =
                            chatData['lastMessageTime'];
                        final String timeAgo = lastMessageTime != null
                            ? _formatTimestamp(lastMessageTime)
                            : '';

                        return ListTile(
                          leading: Stack(
                            children: [
                              CircleAvatar(
                                backgroundColor:
                                    AppTheme.accentColor.withOpacity(0.1),
                                child: Icon(
                                  Icons.chat,
                                  color: AppTheme.accentColor,
                                ),
                              ),
                              if (hasUnread)
                                Positioned(
                                  right: 0,
                                  top: 0,
                                  child: Container(
                                    width: 12,
                                    height: 12,
                                    decoration: BoxDecoration(
                                      color: AppTheme.errorColor,
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: Colors.white,
                                        width: 1.5,
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          title: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  orderTitle,
                                  style: GoogleFonts.poppins(
                                    fontSize: 15,
                                    fontWeight: hasUnread
                                        ? FontWeight.w600
                                        : FontWeight.w500,
                                    color: AppTheme.textPrimary,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (timeAgo.isNotEmpty)
                                Text(
                                  timeAgo,
                                  style: GoogleFonts.inter(
                                    fontSize: 12,
                                    color: AppTheme.textLight,
                                  ),
                                ),
                            ],
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '$otherRole: $otherParticipantId',
                                style: GoogleFonts.inter(
                                  fontSize: 13,
                                  color: AppTheme.textSecondary,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                lastMessage,
                                style: GoogleFonts.inter(
                                  fontSize: 13,
                                  color: hasUnread
                                      ? AppTheme.textPrimary
                                      : AppTheme.textLight,
                                  fontWeight: hasUnread
                                      ? FontWeight.w500
                                      : FontWeight.w400,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ],
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          onTap: () async {
                            // Get the order details from the chat
                            try {
                              final orderDoc = await _firestore
                                  .collection('orders')
                                  .doc(orderId)
                                  .get();

                              if (!mounted) return;

                              if (orderDoc.exists) {
                                final orderData =
                                    orderDoc.data() as Map<String, dynamic>;
                                Navigator.pop(context); // Close dialog

                                // Open the chat dialog
                                showDialog(
                                  context: context,
                                  builder: (context) => ChatDialog(
                                    orderId: orderId,
                                    buyerId: orderData['buyerId'] ?? '',
                                    publisherId: orderData['publisherId'] ?? '',
                                    orderTitle:
                                        orderData['websiteDomainName'] ??
                                            orderTitle,
                                  ),
                                );
                              } else {
                                ToastHelper.showError('Order not found');
                              }
                            } catch (error) {
                              if (!mounted) return;
                              ToastHelper.showError(
                                  'Error loading order: $error');
                            }
                          },
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showNotificationsDialog(
      BuildContext context, Map<String, dynamic> userData) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          width: 400,
          height: 500,
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Notifications',
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const Divider(),
              Expanded(
                child: StreamBuilder<QuerySnapshot>(
                  stream: FirebaseFirestore.instance
                      .collection('users')
                      .doc(FirebaseAuth.instance.currentUser?.uid)
                      .collection('messages')
                      .orderBy('timestamp', descending: true)
                      .limit(50)
                      .snapshots(),
                  builder: (context, snapshot) {
                    // Return a loading indicator while waiting for data
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (snapshot.hasError) {
                      return Center(child: Text('Error: ${snapshot.error}'));
                    }

                    final messages = snapshot.data?.docs ?? [];

                    if (messages.isEmpty) {
                      return const Center(
                        child: Text('No notifications yet'),
                      );
                    }

                    // Mark all as read when opened
                    for (var doc in messages) {
                      if (doc['read'] == false) {
                        FirebaseFirestore.instance
                            .collection('users')
                            .doc(FirebaseAuth.instance.currentUser?.uid)
                            .collection('messages')
                            .doc(doc.id)
                            .update({'read': true});
                      }
                    }

                    return ListView.separated(
                      itemCount: messages.length,
                      separatorBuilder: (context, index) => const Divider(),
                      itemBuilder: (context, index) {
                        final message =
                            messages[index].data() as Map<String, dynamic>;
                        final isRead = message['read'] ?? true;

                        return ListTile(
                          leading: CircleAvatar(
                            backgroundColor: AppTheme.accentColor,
                            child: Icon(
                              _getNotificationIcon(message['type'] ?? 'info'),
                              color: Colors.white,
                            ),
                          ),
                          title: Text(
                            message['message'] ?? 'Notification',
                            style: TextStyle(
                              fontWeight:
                                  isRead ? FontWeight.normal : FontWeight.bold,
                            ),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(message['senderId'] ?? ''),
                              const SizedBox(height: 4),
                              Text(
                                _formatTimestamp(message['timestamp']),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                          contentPadding: EdgeInsets.zero,
                          onTap: () {
                            // Handle notification tap
                            if (message['actionUrl'] != null) {
                              Navigator.pop(context);
                              context.go(message['actionUrl']);
                            }
                          },
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'order':
        return Icons.shopping_cart;
      case 'payment':
        return Icons.payment;
      case 'message':
        return Icons.message;
      default:
        return Icons.notifications;
    }
  }

  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return 'Just now';

    if (timestamp is Timestamp) {
      final now = DateTime.now();
      final date = timestamp.toDate();
      final difference = now.difference(date);

      if (difference.inDays > 7) {
        return DateFormat('MMM d, yyyy').format(date);
      } else if (difference.inDays > 0) {
        return '${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
      } else if (difference.inHours > 0) {
        return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
      } else {
        return 'Just now';
      }
    }

    return 'Just now';
  }

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.sizeOf(context).width;

    return Scaffold(
      key: _scaffoldKey,
      // appBar: AppBar(),
      drawer: StreamBuilder<Map<String, dynamic>?>(
          stream: _authService.streamUserData(),
          builder: (context, snapshot) {
            final userData = snapshot.data ?? {};
            return SideMenu(
              controller: sideMenu,
              showToggle: false,
              alwaysShowFooter: true,
              style: SideMenuStyle(
                itemBorderRadius: BorderRadius.circular(17),
                showTooltip: true,
                displayMode: _sideToggle
                    ? SideMenuDisplayMode.compact
                    : SideMenuDisplayMode.open,
                backgroundColor: const Color.fromARGB(255, 255, 255, 255),
                showHamburger: false,
                iconSize: 25,
                iconSizeExpandable: 25,
                unselectedIconColorExpandable:
                    const Color.fromARGB(75, 255, 255, 255),
                unselectedIconColor:
                    const Color.fromARGB(255, 0, 0, 0).withOpacity(0.5),
                selectedIconColorExpandable:
                    const Color.fromARGB(75, 255, 255, 255),
                unselectedTitleTextStyle:
                    const TextStyle(color: Color.fromARGB(74, 0, 0, 0)),
                itemHeight: 50,
                openSideMenuWidth: 250,
                hoverColor: AppTheme.componentBackColor,
                selectedHoverColor: AppTheme.componentBackColor,
                selectedColor: AppTheme.componentBackColor,
                selectedTitleTextStyle:
                    const TextStyle(color: Color.fromARGB(255, 99, 99, 99)),
                selectedIconColor: Colors.amber,
                arrowCollapse: const Color.fromARGB(255, 255, 255, 255),
                arrowOpen: const Color.fromARGB(255, 255, 255, 255),
                toggleColor: const Color.fromARGB(255, 255, 255, 255),
              ),
              title: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Center(
                  child: Column(
                    children: [
                      Text(
                        '\nGuest',
                        style: GoogleFonts.poppins(
                            fontSize: _sideToggle ? 14 : 44,
                            fontWeight: FontWeight.bold,
                            color: const Color.fromARGB(255, 0, 0, 0),
                            height: 1),
                      ),
                      Text(
                        'Posts\n',
                        style: GoogleFonts.poppins(
                            fontSize: _sideToggle ? 14 : 44,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.accentColor,
                            height: 1),
                      ),
                    ],
                  ),
                ),
              ),
              footer: Padding(
                padding: const EdgeInsets.all(8.0),
                child: InkWell(
                  onTap: () {
                    setState(() {
                      _sideToggle = !_sideToggle;
                    });
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color.fromARGB(255, 255, 193, 7),
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 10, horizontal: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(_sideToggle
                              ? CupertinoIcons.forward
                              : CupertinoIcons.back),
                          if (!_sideToggle) const Text(' Collapse'),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              items: userData['isPublisher'] == true
                  ? [
                      SideMenuItem(
                        title: 'Dashboard',
                        onTap: (index, _) {
                          _navigateToPage(
                            AppRoutes.getPageName(AppRoutes.dashboard),
                            queryParams: widget.queryParams,
                          );
                          setState(() {
                            pageTitle = 'Dashboard';
                          });
                        },
                        icon: const Icon(Icons.web),
                        tooltipContent: "Dashboard",
                      ),
                      SideMenuItem(
                        title: 'My Listings',
                        onTap: (index, _) {
                          _navigateToPage(
                            AppRoutes.getPageName(AppRoutes.listings),
                            queryParams: widget.queryParams,
                          );
                          pageTitle = 'My Websites';
                        },
                        icon: const Icon(CupertinoIcons.link),
                      ),
                      SideMenuItem(
                        title: 'Orders & Requests',
                        onTap: (index, _) {
                          _navigateToPage(
                            AppRoutes.getPageName(AppRoutes.orders),
                            queryParams: widget.queryParams,
                          );
                          pageTitle = 'Orders & Requests';
                        },
                        icon: const Icon(FontAwesomeIcons.tasks),
                        // badgeContent: const Text('3',
                        //     style: TextStyle(color: Colors.white)),
                      ),
                      SideMenuItem(
                        title: 'Earnings & Withdrawals',
                        onTap: (index, _) {
                          _navigateToPage(
                            AppRoutes.getPageName(AppRoutes.earnings),
                            queryParams: widget.queryParams,
                          );
                          pageTitle = 'Earnings & Withdrawals';
                        },
                        icon: const Icon(FontAwesomeIcons.dollarSign),
                      ),
                      SideMenuItem(
                        title: 'Support',
                        onTap: (index, _) {
                          _navigateToPage(
                            AppRoutes.getPageName(AppRoutes.support),
                            queryParams: widget.queryParams,
                          );
                          pageTitle = 'Support';
                        },
                        icon: const Icon(CupertinoIcons.question_diamond),
                      ),
                    ]
                  : [
                      SideMenuItem(
                        title: 'Dashboard',
                        onTap: (index, _) {
                          _navigateToPage(
                            AppRoutes.getPageName(AppRoutes.dashboard),
                            queryParams: widget.queryParams,
                          );
                          pageTitle = 'Dashboard';
                        },
                        icon: const Icon(Icons.web),
                        tooltipContent: "Dashboard",
                      ),
                      SideMenuItem(
                        title: 'All Sites',
                        onTap: (index, _) {
                          _navigateToPage(
                            AppRoutes.getPageName(AppRoutes.listings_buyer),
                            queryParams: widget.queryParams,
                          );
                          pageTitle = 'Find Publishers';
                        },
                        icon: const Icon(CupertinoIcons.link),
                      ),
                      SideMenuItem(
                        title: 'My Orders',
                        onTap: (index, _) {
                          _navigateToPage(
                            AppRoutes.getPageName(AppRoutes.orders_buyer),
                            queryParams: widget.queryParams,
                          );
                          pageTitle = 'My Orders';
                        },
                        icon: const Icon(FontAwesomeIcons.tasks),
                        // badgeContent: const Text('3',
                        //     style: TextStyle(color: Colors.white)),
                      ),
                      SideMenuItem(
                        title: 'Wallet & Payments',
                        onTap: (index, _) {
                          _navigateToPage(
                            AppRoutes.getPageName(AppRoutes.earnings),
                            queryParams: widget.queryParams,
                          );
                          pageTitle = 'Wallet & Payments';
                        },
                        icon: const Icon(FontAwesomeIcons.dollarSign),
                      ),
                      SideMenuItem(
                        title: 'Support',
                        onTap: (index, _) {
                          _navigateToPage(
                            AppRoutes.getPageName(AppRoutes.support_buyer),
                            queryParams: widget.queryParams,
                          );
                          pageTitle = 'Support';
                        },
                        icon: const Icon(CupertinoIcons.question_diamond),
                      ),
                    ],
            );
          }),
      backgroundColor: Colors.white,
      body: StreamBuilder<Map<String, dynamic>?>(
        stream: _authService.streamUserData(),
        builder: (context, snapshot) {
          final userData = snapshot.data ?? {};
          return Row(
            children: [
              // PremiumSidebar(
              //   isAdvertiser: false,
              //   currentRoute: '',
              // ),
              if (width >= 600)
                Container(
                  decoration: BoxDecoration(color: AppTheme.backgroundColor),
                  padding:
                      const EdgeInsets.symmetric(vertical: 18, horizontal: 16),
                  child: InkWell(
                    mouseCursor: MouseCursor.defer,
                    child: SideMenu(
                      controller: sideMenu,
                      showToggle: false,
                      alwaysShowFooter: true,
                      style: SideMenuStyle(
                        itemBorderRadius: BorderRadius.circular(12),
                        showTooltip: true,
                        displayMode: _sideToggle
                            ? SideMenuDisplayMode.compact
                            : SideMenuDisplayMode.open,
                        backgroundColor: AppTheme.backgroundColor,
                        showHamburger: false,
                        iconSize: 25,
                        iconSizeExpandable: 30,
                        unselectedIconColorExpandable:
                            const Color.fromARGB(75, 255, 255, 255),
                        unselectedIconColor:
                            const Color.fromARGB(255, 0, 0, 0).withOpacity(0.5),
                        selectedIconColorExpandable: AppTheme.accentColor,
                        unselectedTitleTextStyle: GoogleFonts.poppins(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                            color: Color.fromARGB(74, 0, 0, 0)),
                        itemHeight: 50,
                        openSideMenuWidth: 265,
                        hoverColor: AppTheme.componentBackColor,
                        // itemOuterPadding: EdgeInsets.all(3),
                        selectedHoverColor:
                            const Color.fromARGB(255, 126, 126, 126),
                        selectedColor: AppTheme.accentColor,
                        selectedTitleTextStyle: GoogleFonts.poppins(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                            color: Color.fromARGB(255, 255, 255, 255)),
                        selectedIconColor:
                            const Color.fromARGB(255, 255, 255, 255),
                        arrowCollapse: const Color.fromARGB(255, 255, 255, 255),
                        arrowOpen: const Color.fromARGB(255, 255, 255, 255),
                        toggleColor: const Color.fromARGB(255, 255, 255, 255),
                      ),
                      title: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Center(
                          child: Column(
                            children: [
                              Text(
                                '\nGuest',
                                style: GoogleFonts.poppins(
                                    fontSize: _sideToggle ? 14 : 44,
                                    fontWeight: FontWeight.bold,
                                    color: const Color.fromARGB(255, 0, 0, 0),
                                    height: 1),
                              ),
                              Text(
                                'Posts\n',
                                style: GoogleFonts.poppins(
                                    fontSize: _sideToggle ? 14 : 44,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.accentColor,
                                    height: 1),
                              ),
                            ],
                          ),
                        ),
                      ),
                      footer: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: InkWell(
                          onTap: () {
                            setState(() {
                              _sideToggle = !_sideToggle;
                            });
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              // color: AppTheme.accentColor,
                              borderRadius: BorderRadius.circular(50),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 10),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                      _sideToggle
                                          ? CupertinoIcons.forward
                                          : CupertinoIcons.back,
                                      color: AppTheme.accentColor),
                                  if (!_sideToggle)
                                    const Text(
                                      ' Collapse',
                                      style: TextStyle(
                                          color: AppTheme.accentColor),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      items: userData['isPublisher'] == true
                          ? [
                              SideMenuItem(
                                title: 'Dashboard',
                                onTap: (index, _) {
                                  _navigateToPage(
                                    AppRoutes.getPageName(AppRoutes.dashboard),
                                    queryParams: widget.queryParams,
                                  );
                                  setState(() {
                                    pageTitle = 'Dashboard';
                                  });
                                },
                                icon: const Icon(Icons.web),
                                tooltipContent: "Dashboard",
                              ),
                              SideMenuItem(
                                title: 'My Listings',
                                onTap: (index, _) {
                                  _navigateToPage(
                                    AppRoutes.getPageName(AppRoutes.listings),
                                    queryParams: widget.queryParams,
                                  );
                                  pageTitle = 'My Websites';
                                },
                                icon: const Icon(CupertinoIcons.link),
                              ),
                              SideMenuItem(
                                title: 'Orders & Requests',
                                onTap: (index, _) {
                                  _navigateToPage(
                                    AppRoutes.getPageName(AppRoutes.orders),
                                    queryParams: widget.queryParams,
                                  );
                                  pageTitle = 'Orders & Requests';
                                },
                                icon: const Icon(FontAwesomeIcons.tasks),
                                // badgeContent: const Text('3',
                                //     style: TextStyle(color: Colors.white)),
                              ),
                              SideMenuItem(
                                title: 'Earnings & Withdrawals',
                                onTap: (index, _) {
                                  _navigateToPage(
                                    AppRoutes.getPageName(AppRoutes.earnings),
                                    queryParams: widget.queryParams,
                                  );
                                  pageTitle = 'Earnings & Withdrawals';
                                },
                                icon: const Icon(FontAwesomeIcons.dollarSign),
                              ),
                              SideMenuItem(
                                title: 'Invoices',
                                onTap: (index, _) {
                                  _navigateToPage(
                                    AppRoutes.getPageName(AppRoutes.invoices),
                                    queryParams: widget.queryParams,
                                  );
                                  pageTitle = 'Invoices';
                                },
                                icon: const Icon(FontAwesomeIcons.fileInvoice),
                              ),
                              SideMenuItem(
                                title: 'Support',
                                onTap: (index, _) {
                                  _navigateToPage(
                                    AppRoutes.getPageName(AppRoutes.support),
                                    queryParams: widget.queryParams,
                                  );
                                  pageTitle = 'Support';
                                },
                                icon:
                                    const Icon(CupertinoIcons.question_diamond),
                              ),
                            ]
                          : [
                              SideMenuItem(
                                title: 'Dashboard',
                                onTap: (index, _) {
                                  _navigateToPage(
                                    AppRoutes.getPageName(AppRoutes.dashboard),
                                    queryParams: widget.queryParams,
                                  );
                                  pageTitle = 'Dashboard';
                                },
                                icon: const Icon(Icons.web),
                                tooltipContent: "Dashboard",
                              ),
                              SideMenuItem(
                                title: 'All Sites',
                                onTap: (index, _) {
                                  _navigateToPage(
                                    AppRoutes.getPageName(
                                        AppRoutes.listings_buyer),
                                    queryParams: widget.queryParams,
                                  );
                                  pageTitle = 'All Sites';
                                },
                                icon: const Icon(CupertinoIcons.link),
                              ),
                              SideMenuItem(
                                title: 'My Orders',
                                onTap: (index, _) {
                                  _navigateToPage(
                                    AppRoutes.getPageName(
                                        AppRoutes.orders_buyer),
                                    queryParams: widget.queryParams,
                                  );
                                  pageTitle = 'My Orders';
                                },
                                icon: const Icon(FontAwesomeIcons.tasks),
                                // badgeContent: const Text('3',
                                //     style: TextStyle(color: Colors.white)),
                              ),
                              SideMenuItem(
                                title: 'Wallet & Payments',
                                onTap: (index, _) {
                                  _navigateToPage(
                                    AppRoutes.getPageName(AppRoutes.earnings),
                                    queryParams: widget.queryParams,
                                  );
                                  pageTitle = 'Wallet & Payments';
                                },
                                icon: const Icon(FontAwesomeIcons.dollarSign),
                              ),
                              SideMenuItem(
                                title: 'Invoices',
                                onTap: (index, _) {
                                  _navigateToPage(
                                    AppRoutes.getPageName(
                                        AppRoutes.invoices_buyer),
                                    queryParams: widget.queryParams,
                                  );
                                  pageTitle = 'Invoices';
                                },
                                icon: const Icon(FontAwesomeIcons.fileInvoice),
                              ),
                              SideMenuItem(
                                title: 'Support',
                                onTap: (index, _) {
                                  _navigateToPage(
                                    AppRoutes.getPageName(
                                        AppRoutes.support_buyer),
                                    queryParams: widget.queryParams,
                                  );
                                  pageTitle = 'Support';
                                },
                                icon:
                                    const Icon(CupertinoIcons.question_diamond),
                              ),
                            ],
                    ),
                  ),
                ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(0.0),
                  child: Column(
                    children: [
                      Container(
                        color: AppTheme.backgroundColor,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: StreamBuilder<Map<String, dynamic>?>(
                            stream: _authService.streamUserData(),
                            builder: (context, snapshot) {
                              final userData = snapshot.data ?? {};

                              return Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  if (width < 600)
                                    IconButton(
                                        onPressed: () => _scaffoldKey
                                            .currentState
                                            ?.openDrawer(),
                                        icon: Icon(Icons.list)),
                                  Text(
                                    pageTitle,
                                    style: GoogleFonts.spaceGrotesk(
                                      fontStyle: FontStyle.normal,
                                      // fontFamily: 'Space',
                                      fontSize: (width < 600) ? 18 : 42,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                  const Spacer(),
                                  Row(
                                    children: [
                                      Stack(
                                        children: [
                                          CircleAvatar(
                                            backgroundColor:
                                                Colors.grey.withOpacity(0.15),
                                            child: IconButton(
                                              icon: const FaIcon(
                                                FontAwesomeIcons.commentDots,
                                                color: Color.fromARGB(
                                                    255, 96, 126, 192),
                                              ),
                                              onPressed: () => _showChatDialog(
                                                  context, userData),
                                            ),
                                          ),
                                          // Unread message badge with count
                                          StreamBuilder<QuerySnapshot>(
                                            stream: FirebaseFirestore.instance
                                                .collection('chats')
                                                .where('participants',
                                                    arrayContains:
                                                        userData['uid'])
                                                .snapshots(),
                                            builder: (context, snapshot) {
                                              if (!snapshot.hasData) {
                                                return const SizedBox.shrink();
                                              }

                                              int unreadCount = 0;
                                              final currentUserId =
                                                  userData['uid'];

                                              // Count unread messages across all chats
                                              for (var chatDoc
                                                  in snapshot.data!.docs) {
                                                final chatData = chatDoc.data()
                                                    as Map<String, dynamic>;
                                                final List<dynamic> unreadFor =
                                                    chatData['unreadFor'] ?? [];

                                                if (unreadFor
                                                    .contains(currentUserId)) {
                                                  unreadCount++;
                                                }
                                              }

                                              // Only show badge if there are unread messages
                                              if (unreadCount == 0) {
                                                return const SizedBox.shrink();
                                              }

                                              return Positioned(
                                                right: 0,
                                                top: 0,
                                                child: Container(
                                                  padding: EdgeInsets.all(
                                                      unreadCount > 9 ? 3 : 4),
                                                  constraints:
                                                      const BoxConstraints(
                                                    minWidth: 18,
                                                    minHeight: 18,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: AppTheme.errorColor,
                                                    shape: BoxShape.circle,
                                                    border: Border.all(
                                                      color: Colors.white,
                                                      width: 1.5,
                                                    ),
                                                  ),
                                                  child: Center(
                                                    child: Text(
                                                      unreadCount > 99
                                                          ? '99+'
                                                          : '$unreadCount',
                                                      style: GoogleFonts.inter(
                                                        color: Colors.white,
                                                        fontSize:
                                                            unreadCount > 9
                                                                ? 9
                                                                : 10,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        ],
                                      ),
                                      const SizedBox(width: 15),
                                      Stack(
                                        children: [
                                          CircleAvatar(
                                            backgroundColor:
                                                Colors.grey.withOpacity(0.15),
                                            child: IconButton(
                                              icon: const FaIcon(
                                                FontAwesomeIcons.bell,
                                                color: Color.fromARGB(
                                                    255, 96, 126, 192),
                                              ),
                                              onPressed: () =>
                                                  _showNotificationsDialog(
                                                      context, userData),
                                            ),
                                          ),
                                          // Notification badge
                                          Positioned(
                                            right: 8,
                                            top: 8,
                                            child: StreamBuilder<QuerySnapshot>(
                                              stream: FirebaseFirestore.instance
                                                  .collection('users')
                                                  .doc(FirebaseAuth.instance
                                                      .currentUser?.uid)
                                                  .collection('messages')
                                                  .where('read',
                                                      isEqualTo: false)
                                                  .snapshots(),
                                              builder: (context, snapshot) {
                                                int count = snapshot.hasData
                                                    ? snapshot.data!.docs.length
                                                    : 0;
                                                return count > 0
                                                    ? Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .all(4),
                                                        decoration:
                                                            const BoxDecoration(
                                                          color: Colors.red,
                                                          shape:
                                                              BoxShape.circle,
                                                        ),
                                                        child: Text(
                                                          count.toString(),
                                                          style:
                                                              const TextStyle(
                                                            color: Colors.white,
                                                            fontSize: 12,
                                                          ),
                                                        ),
                                                      )
                                                    : const SizedBox.shrink();
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(width: 15),
                                      PopupMenuButton<String>(
                                        shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(20)),
                                        constraints: const BoxConstraints(
                                            minWidth: 200, maxWidth: 200),
                                        elevation: 7,
                                        color: const Color.fromARGB(
                                            255, 255, 255, 255),
                                        position: PopupMenuPosition.under,
                                        onSelected: (String value) async {
                                          switch (value) {
                                            case 'profile':
                                              context.go('/profile');
                                              break;
                                            // case 'settings':
                                            //   context.go('/settings');
                                            //   break;
                                            case 'toggle_role':
                                              _changeRole();
                                              break;
                                            case 'signout':
                                              await _authService.signOut();
                                              if (mounted) {
                                                context.go('/auth');
                                              }
                                              break;
                                          }
                                        },
                                        itemBuilder: (BuildContext context) =>
                                            <PopupMenuEntry<String>>[
                                          PopupMenuItem<String>(
                                            value: 'profile',
                                            child: Row(
                                              children: [
                                                Icon(Icons.person,
                                                    color: Colors.black54,
                                                    size: 20),
                                                SizedBox(width: 12),
                                                Text('Profile',
                                                    style: GoogleFonts.poppins(
                                                        color: Colors.black87)),
                                              ],
                                            ),
                                          ),
                                          // PopupMenuItem<String>(
                                          //   value: 'settings',
                                          //   child: Row(
                                          //     children: const [
                                          //       Icon(Icons.settings,
                                          //           color: Colors.black54,
                                          //           size: 20),
                                          //       SizedBox(width: 12),
                                          //       Text('Settings',
                                          //           style: TextStyle(
                                          //               fontFamily: 'Alatsi',
                                          //               color: Colors.black87)),
                                          //     ],
                                          //   ),
                                          // ),
                                          PopupMenuItem<String>(
                                            value: 'toggle_role',
                                            child: Row(
                                              children: [
                                                Icon(
                                                    FontAwesomeIcons
                                                        .arrowsRotate,
                                                    color: Colors.black54,
                                                    size: 20),
                                                SizedBox(width: 12),
                                                Text(
                                                  userData['isPublisher'] ??
                                                          false
                                                      ? 'Convert to Buyer'
                                                      : 'Convert to Publisher',
                                                  style: const TextStyle(
                                                      fontFamily: 'Alatsi',
                                                      color: Colors.black87),
                                                ),
                                              ],
                                            ),
                                          ),
                                          PopupMenuItem<String>(
                                            value: 'signout',
                                            child: Row(
                                              children: const [
                                                Icon(Icons.logout,
                                                    color: Colors.redAccent,
                                                    size: 20),
                                                SizedBox(width: 12),
                                                Text('Sign Out',
                                                    style: TextStyle(
                                                        fontFamily: 'Alatsi',
                                                        color:
                                                            Colors.redAccent)),
                                              ],
                                            ),
                                          ),
                                        ],
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 16, vertical: 8),
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(0),
                                            // color: const Color.fromARGB(
                                            //     255, 255, 255, 255),
                                          ),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              CircleAvatar(
                                                radius: 20,
                                                backgroundImage: userData[
                                                                'profilePictureUrl']
                                                            ?.isNotEmpty ??
                                                        false
                                                    ? CachedNetworkImageProvider(
                                                        userData[
                                                            'profilePictureUrl'])
                                                    : null,
                                                child:
                                                    userData['profilePictureUrl']
                                                                ?.isEmpty ??
                                                            true
                                                        ? const Icon(
                                                            Icons.person)
                                                        : null,
                                              ),
                                              const SizedBox(width: 10),
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    userData['name'] != null
                                                        ? userData['name']
                                                            .toString()
                                                            .split(' ')
                                                            .first
                                                        : 'User',
                                                    style: const TextStyle(
                                                      fontFamily: 'Alatsi',
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 16,
                                                      color: Colors.black87,
                                                      height: 1,
                                                    ),
                                                  ),
                                                  Text(
                                                    userData['isPublisher'] ==
                                                            true
                                                        ? 'Publisher'
                                                        : 'Buyer',
                                                    style: const TextStyle(
                                                        fontFamily: 'Alatsi'),
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(width: 8),
                                              const Icon(Icons.arrow_drop_down,
                                                  color: Colors.black54),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                      ),
                      Expanded(
                        child: RouteAwarePageView(
                          queryParams: widget.queryParams,
                          pageToIndex: _pageToIndex,
                          sideMenuController: sideMenu,
                          userData: userData,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class RouteAwarePageView extends StatefulWidget {
  final Map<String, int> pageToIndex;
  final SideMenuController sideMenuController;
  final Map<String, dynamic> userData;
  final Map<String, dynamic> queryParams;
  const RouteAwarePageView({
    super.key,
    required this.pageToIndex,
    required this.sideMenuController,
    required this.userData,
    required this.queryParams,
  });

  @override
  State<RouteAwarePageView> createState() => _RouteAwarePageViewState();
}

class _RouteAwarePageViewState extends State<RouteAwarePageView> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final router = GoRouter.of(context);
      final currentPage =
          router.routeInformationProvider.value.uri.pathSegments.last;
      final initialIndex = widget.pageToIndex[currentPage] ?? 0;
      if (_pageController.hasClients) {
        _pageController.jumpToPage(initialIndex);
      }
      widget.sideMenuController.changePage(initialIndex);
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<RouteInformation>(
      valueListenable: GoRouter.of(context).routeInformationProvider,
      builder: (context, routeInfo, child) {
        final currentPage = routeInfo.uri.pathSegments.last;
        final currentIndex = widget.pageToIndex[currentPage] ?? 0;

        if (_pageController.hasClients &&
            _pageController.page?.round() != currentIndex) {
          _pageController.jumpToPage(currentIndex);
        }
        if (widget.sideMenuController.currentPage != currentIndex) {
          widget.sideMenuController.changePage(currentIndex);
        }

        return PageView(
          controller: _pageController,
          physics: const NeverScrollableScrollPhysics(),
          children: List.generate(widget.pageToIndex.length, (index) {
            String pageName = widget.pageToIndex.keys.elementAt(index);
            switch (pageName) {
              case 'dashboard':
                return widget.userData['isPublisher'] == true
                    ? const PublisherDashboardWidget()
                    : const BuyerDashboard();
              case 'listings':
                return widget.userData['isPublisher'] == true
                    ? const MyWebsitesWidget()
                    : const FindPublisherPage();
              case 'orders':
                return widget.userData['isPublisher'] == true
                    ? const OrdersAndRequestsPage()
                    : const BuyerOrdersPage();
              case 'earnings':
                return widget.userData['isPublisher'] == true
                    ? EarningsPage()
                    : BuyerFundsPage(authService: AuthService());
              case 'invoices':
                return const PublisherInvoicesPage();
              case 'invoices_buyer':
                return const InvoicesPage();
              case 'support':
                return PublisherSupportPage();
              case 'support_buyer':
                return PublisherSupportPage();
              case 'add-listing':
                return AddWebsitePage();
              case 'profile':
                return ProfilePage();
              case 'profile_buyer':
                return ProfilePage();
              default:
                return Center(child: Text(pageName));
            }
          }),
        );
      },
    );
  }
}
