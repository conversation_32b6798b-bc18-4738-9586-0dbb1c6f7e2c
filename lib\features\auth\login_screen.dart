// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:guest_posts/core/services/auth_service.dart';
import 'package:guest_posts/core/utils/colors.dart';
import 'package:guest_posts/core/utils/toast_helper.dart';
import 'package:guest_posts/core/theme/app_theme.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final AuthService _authService = AuthService();
  bool _isLoading = false;

  void _login() async {
    if (!_formKey.currentState!.validate()) return;
    if (_isLoading) return;

    setState(() => _isLoading = true);

    final result = await _authService.loginWithEmail(
      emailController.text.trim(),
      passwordController.text,
    );

    setState(() => _isLoading = false);

    if (result && mounted) {
      ToastHelper.showSuccess('Login successful!');
      context.go('/home');
    } else if (mounted) {
      // Show error message
      ToastHelper.showError('Login failed. Please check your credentials.');
    }
  }

  void _loginWithGoogle() async {
    setState(() => _isLoading = true);
    bool success = await AuthService().signInOrSignUpWithGoogle();
    if (success) context.go('/home');

    setState(() => _isLoading = false);
  }

  Color _currentColor = const Color.fromARGB(255, 150, 0, 0);
  double _leftPosition = -5;
  double _bottomPosition = -10;

  // Lists of shapes and colors to cycle through
  final List<Color> _colors = [
    AppTheme.accentColor,
    const Color.fromARGB(255, 136, 5, 224),
    Colors.blue,
    Colors.green,
    Colors.purple,
  ];

  int _colorIndex = 0;

  bool _rememberMe = false;
  bool _showPass = false;

  // Timer for automatic changes
  late Timer _timer;

  @override
  void initState() {
    super.initState();
    // Start the timer to change every 2 seconds
    _timer = Timer.periodic(Duration(seconds: 3), (timer) {
      _changeAppearance();
    });
  }

  @override
  void dispose() {
    _timer.cancel(); // Cancel the timer when the widget is disposed
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  // Function to update shape, color, and position
  void _changeAppearance() {
    setState(() {
      _colorIndex = (_colorIndex + 1) % _colors.length;
      _currentColor = _colors[_colorIndex];
      _leftPosition += 10; // Move horizontally
      _bottomPosition += 5; // Move vertically

      // Optional: Reset position if it goes off-screen
      if (_leftPosition > MediaQuery.of(context).size.width) {
        _leftPosition = -5;
      }
      if (_bottomPosition > MediaQuery.of(context).size.height) {
        _bottomPosition = -10;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Gradient background with blur

          Positioned(
            left: _leftPosition,
            bottom: _bottomPosition,
            child: AnimatedContainer(
              duration: Duration(seconds: 2),
              width: 300,
              height: 300,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _currentColor,
              ),
            ),
          ),
          Positioned(
            right: 300,
            top: 300,
            child: AnimatedContainer(
              duration: Duration(seconds: 2),
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _currentColor,
              ),
            ),
          ),
          Positioned(
            left: _leftPosition,
            top: _bottomPosition,
            child: AnimatedContainer(
              duration: Duration(seconds: 2),
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _currentColor,
              ),
            ),
          ),
          // Partial blurred background (e.g., top section)
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 100.0, sigmaY: 100.0),
            child: Container(
              color: const Color.fromARGB(143, 255, 255, 255),
            ),
          ),
          // Main content
          Center(
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: SizedBox(
                  width: 400,
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Logo
                        CircleAvatar(
                          radius: 40,
                          backgroundColor: AppTheme.accentColor,
                          child: Text(
                            'GP',
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'cairo'),
                          ),
                        ),
                        SizedBox(height: 100),
                        // Log In text
                        Text(
                          'Log In',
                          style: TextStyle(
                            fontSize: 60,
                            fontWeight: FontWeight.w200,
                            fontFamily: 'Alatsi',
                            color: const Color.fromARGB(255, 5, 25, 68),
                          ),
                        ),
                        SizedBox(height: 30),
                        // Social login buttons
                        SizedBox(
                          height: 50,
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: () {
                              _loginWithGoogle();
                            },
                            icon: Icon(
                              FontAwesomeIcons.google,
                              color: const Color.fromARGB(255, 0, 0, 0),
                              size: 25,
                            ),
                            label: Text(' Log in with Google'),
                            style: ElevatedButton.styleFrom(
                              elevation: 0,
                              textStyle:
                                  TextStyle(fontFamily: 'Alatsi', fontSize: 20),
                              foregroundColor:
                                  const Color.fromARGB(255, 0, 0, 0),
                              backgroundColor: AppTheme.componentBackColor,
                              //side: BorderSide(color: Colors.grey.shade300),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(25),
                              ),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 32, vertical: 16),
                            ),
                          ),
                        ),
                        SizedBox(height: 20),
                        // "Or continue with" text
                        Row(
                          children: [
                            Expanded(
                              child: Divider(
                                color: AppTheme.lightGrey,
                              ),
                            ),
                            Text(
                              '  OR Continue With  ',
                              style: TextStyle(
                                  color: AppTheme.accentColor, fontSize: 20),
                            ),
                            Expanded(
                              child: Divider(
                                color: AppTheme.lightGrey,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 20),
                        // Email field
                        SizedBox(
                          width: 400,
                          height: 50,
                          child: TextFormField(
                            controller: emailController,
                            keyboardType: TextInputType.emailAddress,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your email';
                              }
                              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                                  .hasMatch(value)) {
                                return 'Please enter a valid email';
                              }
                              return null;
                            },
                            decoration: InputDecoration(
                              hintText: 'Email',
                              filled: true,
                              fillColor: AppTheme.componentBackColor,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide.none,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 10),
                        // Password field
                        SizedBox(
                          width: 400,
                          height: 50,
                          child: TextFormField(
                            controller: passwordController,
                            obscureText: _showPass ? false : true,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your password';
                              }
                              if (value.length < 6) {
                                return 'Password must be at least 6 characters';
                              }
                              return null;
                            },
                            decoration: InputDecoration(
                              hintText: 'Password',
                              filled: true,
                              fillColor: AppTheme.componentBackColor,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide.none,
                              ),
                              suffix: InkWell(
                                  onTap: () {
                                    setState(() {
                                      _showPass = !_showPass;
                                    });
                                  },
                                  child: Text(_showPass ? 'Hide' : 'Show')),
                              suffixStyle: TextStyle(
                                  color: AppTheme.accentColor, fontSize: 18),
                            ),
                          ),
                        ),
                        SizedBox(height: 10),
                        // Remember me and Lost password
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Checkbox(
                                  value: _rememberMe,
                                  onChanged: (value) {
                                    setState(() {
                                      _rememberMe = value!;
                                    });
                                  },
                                  checkColor: AppTheme.accentColor,
                                  shape: RoundedRectangleBorder(
                                      side: BorderSide(),
                                      borderRadius: BorderRadius.circular(3)),
                                  activeColor: AppTheme.accentColor,
                                ),
                                Text('Remember me'),
                              ],
                            ),
                            TextButton(
                              onPressed: () {
                                _showResetPasswordDialog();
                              },
                              child: Text(
                                'Lost password?',
                                style: TextStyle(
                                  color: AppTheme.accentColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 20),
                        // Log In button
                        SizedBox(
                          width: 400,
                          height: 50,
                          child: ElevatedButton(
                            onPressed: () {
                              _login();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.black87,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(25),
                              ),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 40, vertical: 15),
                            ),
                            child: Text(
                              'Log In',
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                        ),
                        SizedBox(height: 20),
                        // Create new account
                        InkWell(
                          onTap: () {
                            // Navigator.push(
                            //     context,
                            //     MaterialPageRoute(
                            //       builder: (context) => SignUpScreen(),
                            //     ));
                            context.go('/auth/register');
                          },
                          child: RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: 'Has no account yet? ',
                                  style: TextStyle(
                                    color: Colors
                                        .black, // Style for the first part
                                    fontSize: 16,
                                  ),
                                ),
                                TextSpan(
                                  text: 'Create a new account now',
                                  style: TextStyle(
                                    color: AppTheme.accentColor,
                                    fontSize: 16,
                                    fontWeight: FontWeight
                                        .bold, // Optional: make it bold
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          // Cookie settings button (bottom-left)
          // Positioned(
          //   bottom: 20,
          //   left: 20,
          //   child: ElevatedButton(
          //     onPressed: () {},
          //     style: ElevatedButton.styleFrom(
          //       foregroundColor: Colors.black,
          //       backgroundColor: Colors.white,
          //       side: BorderSide(color: Colors.grey.shade300),
          //       shape: RoundedRectangleBorder(
          //         borderRadius: BorderRadius.circular(25),
          //       ),
          //     ),
          //     child: Text('COOKIE SETTINGS'),
          //   ),
          // ),
        ],
      ),
    );
  }

  void _showResetPasswordDialog() {
    final TextEditingController resetEmailController = TextEditingController();
    final GlobalKey<FormState> resetFormKey = GlobalKey<FormState>();
    bool isResetting = false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Reset Password'),
          content: Form(
            key: resetFormKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Enter your email address and we\'ll send you a link to reset your password.',
                  style: TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: resetEmailController,
                  keyboardType: TextInputType.emailAddress,
                  decoration: InputDecoration(
                    hintText: 'Email',
                    filled: true,
                    fillColor: AppTheme.componentBackColor,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: BorderSide.none,
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your email';
                    }
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                        .hasMatch(value)) {
                      return 'Please enter a valid email';
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: isResetting
                  ? null
                  : () async {
                      if (resetFormKey.currentState!.validate()) {
                        setState(() => isResetting = true);

                        final success = await _authService.resetPassword(
                          resetEmailController.text.trim(),
                        );

                        if (mounted) {
                          Navigator.pop(context);

                          if (success) {
                            ToastHelper.showSuccess(
                                'Password reset email sent. Please check your inbox.');
                          } else {
                            ToastHelper.showError(
                                'Failed to send reset email. Please try again.');
                          }
                        }
                      }
                    },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber,
              ),
              child: isResetting
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('Send Reset Link'),
            ),
          ],
        ),
      ),
    );
  }
}
