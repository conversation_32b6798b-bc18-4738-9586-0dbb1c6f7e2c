/* eslint-disable object-curly-spacing */
/* eslint-disable valid-jsdoc */
/* eslint-disable no-trailing-spaces */
/* eslint-disable indent */
/* eslint-disable max-len */
const functions = require("firebase-functions/v1");
const admin = require("firebase-admin");
const PDFDocument = require("pdfkit");
const os = require("os");
const path = require("path");
const fs = require("fs");

// Note: We don't need to call admin.initializeApp() here because it's already initialized in index.js

// Get the default bucket from Firebase Admin
const bucket = admin.storage().bucket();

// Company details - should be moved to Firestore settings
const companyDetails = {
  name: "Guest Posts Links",
  address: "123 Web Street, Internet City, 12345",
  email: "<EMAIL>",
  phone: "+****************",
  website: "https://guestpostlinks.com",
  logo: "https://guestpostslinks.com/assets/images/logo/logo2.png", // Replace with actual logo URL
};

/**
 * Generates a PDF invoice and stores it in Firebase Storage
 */
exports.generateInvoicePdf = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
  }

  const { invoiceId } = data;
  if (!invoiceId) {
    throw new functions.https.HttpsError("invalid-argument", "Invoice ID is required");
  }

  try {
    // Use the helper function to regenerate the PDF
    const result = await regenerateInvoicePdf(invoiceId);
    return result;
  } catch (error) {
    console.error("Error generating invoice PDF:", error);
    throw new functions.https.HttpsError("internal", `Error generating invoice PDF: ${error.message}`);
  }
});

/**
 * Helper function to regenerate a PDF for an invoice
 */
async function regenerateInvoicePdf(invoiceId) {
  // Get invoice data
  const invoiceDoc = await admin.firestore().collection("invoices").doc(invoiceId).get();
  if (!invoiceDoc.exists) {
    throw new Error("Invoice not found");
  }

  const invoice = invoiceDoc.data();
  const tempFilePath = path.join(os.tmpdir(), `invoice_${invoiceId}.pdf`);

  // Create PDF
  await createInvoicePdf(invoice, tempFilePath);

  // Upload to Firebase Storage
  const destination = `invoices/${invoice.userId}/${invoiceId}.pdf`;
  let url;

  console.log(`Uploading regenerated PDF to ${destination}`);

  try {
    // Make sure the file exists
    if (!fs.existsSync(tempFilePath)) {
      throw new Error(`Temp file does not exist at ${tempFilePath}`);
    }

    // Upload the file
    await bucket.upload(tempFilePath, {
      destination: destination,
      metadata: {
        contentType: "application/pdf",
      },
      public: true, // Make the file publicly accessible
    });

    // Make the file publicly accessible
    await bucket.file(destination).makePublic();

    console.log(`PDF uploaded successfully to ${destination}`);

    // Get download URL - try both methods
    const file = bucket.file(destination);

    // Method 1: Get public URL
    url = `https://storage.googleapis.com/${bucket.name}/${destination}`;

    // Method 2: Get signed URL as backup
    try {
      const [signedUrl] = await file.getSignedUrl({
        action: "read",
        expires: "03-01-2500", // Far future expiration
      });
      url = signedUrl;
    } catch (signUrlError) {
      console.log(`Error getting signed URL, using public URL instead: ${signUrlError}`);
    }
    console.log(`Generated signed URL: ${url}`);
  } catch (uploadError) {
    console.error(`Error uploading PDF: ${uploadError}`);
    throw uploadError;
  }

  // Update invoice with PDF URL
  await admin.firestore().collection("invoices").doc(invoiceId).update({
    pdfUrl: url,
  });

  // Also update in user's subcollection if userId exists
  if (invoice.userId) {
    await admin.firestore()
      .collection("users")
      .doc(invoice.userId)
      .collection("invoices")
      .doc(invoiceId)
      .update({
        pdfUrl: url,
      });
  }

  // Clean up temp file
  fs.unlinkSync(tempFilePath);

  return { success: true, pdfUrl: url };
}

/**
 * Creates a PDF invoice
 */
async function createInvoicePdf(invoice, outputPath) {
  return new Promise((resolve, reject) => {
    try {
      const doc = new PDFDocument({ margin: 50 });
      const writeStream = fs.createWriteStream(outputPath);

      writeStream.on("finish", () => {
        resolve();
      });

      doc.pipe(writeStream);

      // Add company logo
      // doc.image(companyDetails.logo, 50, 45, { width: 150 });

      // Add company details
      doc.fontSize(20).text(companyDetails.name, 50, 50);
      doc.fontSize(10)
        .text(companyDetails.address, 50, 80)
        .text(`Email: ${companyDetails.email}`, 50, 95)
        .text(`Phone: ${companyDetails.phone}`, 50, 110)
        .text(`Website: ${companyDetails.website}`, 50, 125);

      // Add invoice details
      doc.fontSize(16).text("INVOICE", 400, 50);
      doc.fontSize(10)
        .text(`Invoice #: ${invoice.invoiceId}`, 400, 80)
        .text(`Date: ${invoice.createdAt.toDate().toLocaleDateString()}`, 400, 95)
        .text(`Status: ${invoice.status}`, 400, 110);

      // Add customer details
      doc.fontSize(14).text("Bill To:", 50, 170);
      doc.fontSize(10)
        .text(`Name: ${invoice.userName}`, 50, 190)
        .text(`Email: ${invoice.userEmail}`, 50, 205);

      // Add invoice type header
      let invoiceTypeText = "Deposit";
      if (invoice.invoiceType === "order") {
        invoiceTypeText = "Order Purchase";
      } else if (invoice.invoiceType === "withdrawal") {
        invoiceTypeText = "Withdrawal";
      }

      doc.fontSize(14).text(`${invoiceTypeText} Details:`, 50, 240);

      // Add table header
      doc.fontSize(10)
        .text("Description", 50, 270)
        .text("Amount", 400, 270);

      doc.moveTo(50, 290).lineTo(550, 290).stroke();

      // Add transaction details
      let yPos = 310;

      if (invoice.invoiceType === "deposit") {
        doc.text(`Wallet Deposit (${invoice.paymentMethod})`, 50, yPos);
        doc.text(`$${invoice.amount.toFixed(2)}`, 400, yPos);
        yPos += 20;

        if (invoice.fees) {
          doc.text("Processing Fee", 50, yPos);
          doc.text(`$${invoice.fees.toFixed(2)}`, 400, yPos);
          yPos += 20;
        }
      } else if (invoice.invoiceType === "order") {
        if (invoice.orderDetails) {
          doc.text(`Order #${invoice.orderId}`, 50, yPos);
          doc.text(`$${invoice.amount.toFixed(2)}`, 400, yPos);
          yPos += 20;

          if (invoice.orderDetails.websiteUrl) {
            doc.text(`Website: ${invoice.orderDetails.websiteUrl}`, 70, yPos);
            yPos += 20;
          }

          if (invoice.orderDetails.postTitle) {
            doc.text(`Post Title: ${invoice.orderDetails.postTitle}`, 70, yPos);
            yPos += 20;
          }
        } else {
          doc.text(`Order Purchase #${invoice.orderId}`, 50, yPos);
          doc.text(`$${invoice.amount.toFixed(2)}`, 400, yPos);
          yPos += 20;
        }
      } else if (invoice.invoiceType === "withdrawal") {
        doc.text(`Wallet Withdrawal (${invoice.paymentMethod})`, 50, yPos);
        doc.text(`$${invoice.amount.toFixed(2)}`, 400, yPos);
        yPos += 20;

        if (invoice.fees) {
          doc.text("Processing Fee", 50, yPos);
          doc.text(`$${invoice.fees.toFixed(2)}`, 400, yPos);
          yPos += 20;
        }
      }

      // Add total
      doc.moveTo(50, yPos).lineTo(550, yPos).stroke();
      yPos += 20;
      doc.fontSize(12).text("Total:", 300, yPos);
      doc.fontSize(12).text(`$${invoice.totalAmount.toFixed(2)}`, 400, yPos);

      // Add footer
      doc.fontSize(10).text("Thank you for your business!", 50, 700);

      doc.end();
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Automatically generates a PDF invoice when a new invoice is created in Firestore
 */
exports.autoGenerateInvoicePdf = functions.firestore
  .document("invoices/{invoiceId}")
  .onCreate(async (_, context) => {
    const invoiceId = context.params.invoiceId;

    console.log(`Auto-generating PDF for invoice ${invoiceId}`);

    try {
      // Use the helper function to generate the PDF
      await regenerateInvoicePdf(invoiceId);

      console.log(`Successfully generated PDF for invoice ${invoiceId}`);
      return { success: true };
    } catch (error) {
      console.error(`Error auto-generating invoice PDF: ${error}`);
      // Don't throw here, as it would cause the function to retry
      return { success: false, error: error.message };
    }
  });

/**
 * Automatically updates invoice status when an order status changes
 */
exports.syncInvoiceStatus = functions.firestore
  .document("orders/{orderId}")
  .onUpdate(async (change, context) => {
    const orderId = context.params.orderId;
    const beforeData = change.before.data();
    const afterData = change.after.data();

    // Only proceed if the status has changed
    if (beforeData.status === afterData.status) {
      console.log(`Order ${orderId} status unchanged, skipping invoice update`);
      return null;
    }

    const newStatus = afterData.status;
    console.log(`Order ${orderId} status changed to ${newStatus}, updating related invoices`);

    try {
      // Find all invoices related to this order
      const invoicesSnapshot = await admin.firestore()
        .collection("invoices")
        .where("orderId", "==", orderId)
        .get();

      if (invoicesSnapshot.empty) {
        console.log(`No invoices found for order ${orderId}`);
        return null;
      }

      // Map order status to invoice status
      let invoiceStatus;
      switch (newStatus) {
        case "Completed":
          invoiceStatus = "Completed";
          break;
        case "Cancelled":
        case "Declined":
          invoiceStatus = "Cancelled";
          break;
        case "Approved":
          invoiceStatus = "Approved";
          break;
        case "Pending":
          invoiceStatus = "Pending";
          break;
        case "In Progress":
          invoiceStatus = "In Progress";
          break;
        default:
          invoiceStatus = newStatus; // Use the same status if no mapping
      }

      console.log(`Updating ${invoicesSnapshot.size} invoice(s) for order ${orderId} to status: ${invoiceStatus}`);

      // Update each invoice
      const batch = admin.firestore().batch();

      invoicesSnapshot.forEach((doc) => {
        const invoiceId = doc.id;
        const invoice = doc.data();

        // Update main invoice
        batch.update(doc.ref, { status: invoiceStatus });

        // Also update in user's subcollection if userId exists
        if (invoice.userId) {
          const userInvoiceRef = admin.firestore()
            .collection("users")
            .doc(invoice.userId)
            .collection("invoices")
            .doc(invoiceId);

          batch.update(userInvoiceRef, { status: invoiceStatus });
        }

        console.log(`Added invoice ${invoiceId} to batch update`);
      });

      // Commit all updates
      await batch.commit();
      console.log(`Successfully updated all invoices for order ${orderId}`);

      // Regenerate PDFs for all updated invoices
      console.log(`Regenerating PDFs for ${invoicesSnapshot.size} updated invoices`);

      for (const doc of invoicesSnapshot.docs) {
        const invoiceId = doc.id;
        try {
          // Call the PDF generation function for each invoice
          await regenerateInvoicePdf(invoiceId);
          console.log(`Successfully regenerated PDF for invoice ${invoiceId}`);
        } catch (pdfError) {
          console.error(`Error regenerating PDF for invoice ${invoiceId}: ${pdfError}`);
          // Continue with other invoices even if one fails
        }
      }

      return { success: true, updatedCount: invoicesSnapshot.size };
    } catch (error) {
      console.error(`Error updating invoices for order ${orderId}: ${error}`);
      // Don't throw here, as it would cause the function to retry
      return { success: false, error: error.message };
    }
  });

/**
 * Automatically updates invoice status when a withdrawal status changes
 */
exports.syncWithdrawalInvoiceStatus = functions.firestore
  .document("withdrawals/{withdrawalId}")
  .onUpdate(async (change, context) => {
    const withdrawalId = context.params.withdrawalId;
    const beforeData = change.before.data();
    const afterData = change.after.data();

    // Only proceed if the status has changed
    if (beforeData.status === afterData.status) {
      console.log(`Withdrawal ${withdrawalId} status unchanged, skipping invoice update`);
      return null;
    }

    const newStatus = afterData.status;
    console.log(`Withdrawal ${withdrawalId} status changed to ${newStatus}, updating related invoices`);

    try {
      // Find all invoices related to this withdrawal
      const invoicesSnapshot = await admin.firestore()
        .collection("invoices")
        .where("withdrawalId", "==", withdrawalId)
        .get();

      if (invoicesSnapshot.empty) {
        console.log(`No invoices found for withdrawal ${withdrawalId}`);
        return null;
      }

      // Map withdrawal status to invoice status
      let invoiceStatus;
      switch (newStatus) {
        case "Completed":
          invoiceStatus = "Completed";
          break;
        case "Rejected":
          invoiceStatus = "Cancelled";
          break;
        case "Pending":
          invoiceStatus = "Pending";
          break;
        default:
          invoiceStatus = newStatus; // Use the same status if no mapping
      }

      console.log(`Updating ${invoicesSnapshot.size} invoice(s) for withdrawal ${withdrawalId} to status: ${invoiceStatus}`);

      // Update each invoice
      const batch = admin.firestore().batch();

      invoicesSnapshot.forEach((doc) => {
        const invoiceId = doc.id;
        const invoice = doc.data();

        // Update main invoice
        batch.update(doc.ref, { status: invoiceStatus });

        // Also update in user's subcollection if userId exists
        if (invoice.userId) {
          const userInvoiceRef = admin.firestore()
            .collection("users")
            .doc(invoice.userId)
            .collection("invoices")
            .doc(invoiceId);

          batch.update(userInvoiceRef, { status: invoiceStatus });
        }

        console.log(`Added invoice ${invoiceId} to batch update`);
      });

      // Commit all updates
      await batch.commit();
      console.log(`Successfully updated all invoices for withdrawal ${withdrawalId}`);

      // Regenerate PDFs for all updated invoices
      console.log(`Regenerating PDFs for ${invoicesSnapshot.size} updated invoices`);

      for (const doc of invoicesSnapshot.docs) {
        const invoiceId = doc.id;
        try {
          // Call the PDF generation function for each invoice
          await regenerateInvoicePdf(invoiceId);
          console.log(`Successfully regenerated PDF for invoice ${invoiceId}`);
        } catch (pdfError) {
          console.error(`Error regenerating PDF for invoice ${invoiceId}: ${pdfError}`);
          // Continue with other invoices even if one fails
        }
      }

      return { success: true, updatedCount: invoicesSnapshot.size };
    } catch (error) {
      console.error(`Error updating invoices for withdrawal ${withdrawalId}: ${error}`);
      // Don't throw here, as it would cause the function to retry
      return { success: false, error: error.message };
    }
  });

/**
 * Automatically updates invoice status when a deposit status changes
 * This handles both Stripe and PayPal deposits by checking the idempotency collection
 */
exports.syncDepositInvoiceStatus = functions.firestore
  .document("idempotency/{paymentId}")
  .onUpdate(async (change, context) => {
    const paymentId = context.params.paymentId;
    const beforeData = change.before.data();
    const afterData = change.after.data();

    // Only proceed if the processed status has changed
    if (beforeData.processed === afterData.processed) {
      console.log(`Payment ${paymentId} processed status unchanged, skipping invoice update`);
      return null;
    }

    // Only proceed if the payment is now processed
    if (!afterData.processed) {
      console.log(`Payment ${paymentId} not processed yet, skipping invoice update`);
      return null;
    }

    console.log(`Payment ${paymentId} now processed, updating related invoices`);

    try {
      // Find all invoices related to this payment ID
      // We need to check multiple fields since different payment methods store IDs differently
      const stripeInvoicesSnapshot = await admin.firestore()
        .collection("invoices")
        .where("depositDetails.paymentIntentId", "==", paymentId)
        .get();

      const paypalInvoicesSnapshot = await admin.firestore()
        .collection("invoices")
        .where("depositDetails.paypalOrderId", "==", paymentId)
        .get();

      // Combine results
      const invoiceDocs = [...stripeInvoicesSnapshot.docs, ...paypalInvoicesSnapshot.docs];

      if (invoiceDocs.length === 0) {
        console.log(`No invoices found for payment ${paymentId}`);
        return null;
      }

      console.log(`Updating ${invoiceDocs.length} invoice(s) for payment ${paymentId} to status: Completed`);

      // Update each invoice
      const batch = admin.firestore().batch();

      invoiceDocs.forEach((doc) => {
        const invoiceId = doc.id;
        const invoice = doc.data();

        // Update main invoice
        batch.update(doc.ref, { status: "Completed" });

        // Also update in user's subcollection if userId exists
        if (invoice.userId) {
          const userInvoiceRef = admin.firestore()
            .collection("users")
            .doc(invoice.userId)
            .collection("invoices")
            .doc(invoiceId);

          batch.update(userInvoiceRef, { status: "Completed" });
        }

        console.log(`Added invoice ${invoiceId} to batch update`);
      });

      // Commit all updates
      await batch.commit();
      console.log(`Successfully updated all invoices for payment ${paymentId}`);

      // Regenerate PDFs for all updated invoices
      console.log(`Regenerating PDFs for ${invoiceDocs.length} updated invoices`);

      for (const doc of invoiceDocs) {
        const invoiceId = doc.id;
        try {
          // Call the PDF generation function for each invoice
          await regenerateInvoicePdf(invoiceId);
          console.log(`Successfully regenerated PDF for invoice ${invoiceId}`);
        } catch (pdfError) {
          console.error(`Error regenerating PDF for invoice ${invoiceId}: ${pdfError}`);
          // Continue with other invoices even if one fails
        }
      }

      return { success: true, updatedCount: invoiceDocs.length };
    } catch (error) {
      console.error(`Error updating invoices for payment ${paymentId}: ${error}`);
      // Don't throw here, as it would cause the function to retry
      return { success: false, error: error.message };
    }
  },
  );
