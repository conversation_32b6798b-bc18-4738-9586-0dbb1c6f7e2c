import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Premium app theme configuration
/// This class centralizes all theme-related styling for the app
class AppTheme {
  // Premium color palette
  static const Color primaryColor = Color(0xFF2563EB); // Rich blue
  static const Color secondaryColor = Color(0xFF4F46E5); // Indigo
  static const Color accentColor = Color(0xFF2563EB); // Rich blue
  static const Color accentGradientStart = Color(0xFF2563EB); // Rich blue
  static const Color accentGradientEnd = Color(0xFF3B82F6); // Lighter blue

  // Text colors
  static const Color textPrimary = Color(0xFF0F172A); // Very dark blue/slate
  static const Color textSecondary = Color(0xFF475569); // Slate
  static const Color textLight = Color(0xFF94A3B8); // Light slate

  // Background colors
  static const Color backgroundColor = Color(0xFFF8FAFC); // Very light slate
  static const Color surfaceColor = Colors.white;
  static const Color componentBackColor = Color(0xFFF1F5F9); // Light slate
  static const Color lightGrey = Color(0xFFF8FAFC); // Very light slate

  // Border and divider colors
  static const Color borderColor = Color(0xFFE2E8F0); // Light slate
  static const Color dividerColor = Color(0xFFE2E8F0); // Light slate

  // Status colors
  static const Color successColor = Color(0xFF10B981); // Emerald
  static const Color errorColor = Color(0xFFEF4444); // Red
  static const Color warningColor = Color(0xFFF59E0B); // Amber
  static const Color infoColor = Color(0xFF3B82F6); // Blue

  // Shadow colors
  static const Color cardShadow = Color(0x0F000000);

  // Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [accentGradientStart, accentGradientEnd],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Typography - Font sizes
  static const double fontSizeXS = 12.0;
  static const double fontSizeS = 14.0;
  static const double fontSizeM = 16.0;
  static const double fontSizeL = 18.0;
  static const double fontSizeXL = 20.0;
  static const double fontSizeXXL = 24.0;
  static const double fontSizeHuge = 36.0; // Increased for more impact
  static const double fontSizeDisplay = 48.0; // Added for hero sections

  // Spacing
  static const double spacingXS = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXL = 32.0;
  static const double spacingXXL = 48.0;

  // Border radius
  static const double borderRadiusS = 4.0;
  static const double borderRadiusM = 8.0;
  static const double borderRadiusL = 12.0;
  static const double borderRadiusXL = 16.0;
  static const double borderRadiusXXL = 24.0;
  static const double borderRadiusRound = 100.0;

  // Elevation
  static const double elevationXS = 1.0;
  static const double elevationS = 2.0;
  static const double elevationM = 4.0;
  static const double elevationL = 8.0;
  static const double elevationXL = 16.0;

  // Animation durations
  static const Duration durationFast = Duration(milliseconds: 150);
  static const Duration durationMedium = Duration(milliseconds: 300);
  static const Duration durationSlow = Duration(milliseconds: 500);

  // Get the app theme data
  static ThemeData getTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.light(
        primary: primaryColor,
        secondary: secondaryColor,
        surface: surfaceColor,
        background: backgroundColor,
        error: errorColor,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: textPrimary,
        onBackground: textPrimary,
        onError: Colors.white,
      ),

      // Typography
      textTheme: GoogleFonts.interTextTheme().copyWith(
        displayLarge: GoogleFonts.poppins(
          fontSize: fontSizeDisplay,
          fontWeight: FontWeight.bold,
          color: textPrimary,
          height: 1.1, // Tighter line height for headings
          letterSpacing: -0.5, // Slightly tighter letter spacing
        ),
        displayMedium: GoogleFonts.poppins(
          fontSize: fontSizeHuge,
          fontWeight: FontWeight.bold,
          color: textPrimary,
          height: 1.2,
          letterSpacing: -0.3,
        ),
        displaySmall: GoogleFonts.poppins(
          fontSize: fontSizeXXL,
          fontWeight: FontWeight.bold,
          color: textPrimary,
          height: 1.3,
        ),
        headlineMedium: GoogleFonts.poppins(
          fontSize: fontSizeXL,
          fontWeight: FontWeight.w600,
          color: textPrimary,
          height: 1.4,
        ),
        titleLarge: GoogleFonts.poppins(
          fontSize: fontSizeL,
          fontWeight: FontWeight.w600,
          color: textPrimary,
        ),
        titleMedium: GoogleFonts.poppins(
          fontSize: fontSizeM,
          fontWeight: FontWeight.w600,
          color: textPrimary,
        ),
        titleSmall: GoogleFonts.poppins(
          fontSize: fontSizeS,
          fontWeight: FontWeight.w600,
          color: textPrimary,
        ),
        bodyLarge: GoogleFonts.inter(
          fontSize: fontSizeM,
          fontWeight: FontWeight.normal,
          color: textPrimary,
          height: 1.6, // Better line height for readability
        ),
        bodyMedium: GoogleFonts.inter(
          fontSize: fontSizeS,
          fontWeight: FontWeight.normal,
          color: textPrimary,
          height: 1.6,
        ),
        bodySmall: GoogleFonts.inter(
          fontSize: fontSizeXS,
          fontWeight: FontWeight.normal,
          color: textSecondary,
          height: 1.5,
        ),
        labelLarge: GoogleFonts.inter(
          fontSize: fontSizeS,
          fontWeight: FontWeight.w500,
          color: textPrimary,
        ),
      ),

      // Component themes
      appBarTheme: AppBarTheme(
        backgroundColor: surfaceColor,
        elevation: 0,
        centerTitle: false,
        titleTextStyle: GoogleFonts.poppins(
          fontSize: fontSizeL,
          fontWeight: FontWeight.w600,
          color: textPrimary,
        ),
        iconTheme: const IconThemeData(color: textPrimary),
      ),

      cardTheme: CardTheme(
        color: surfaceColor,
        elevation: elevationS,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusL),
          side: BorderSide(color: borderColor),
        ),
        margin: const EdgeInsets.all(spacingS),
      ),

      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.resolveWith<Color>(
            (Set<MaterialState> states) {
              if (states.contains(MaterialState.disabled)) {
                return primaryColor.withOpacity(0.5);
              }
              if (states.contains(MaterialState.hovered)) {
                return primaryColor.withOpacity(0.9);
              }
              return primaryColor;
            },
          ),
          foregroundColor: MaterialStateProperty.all(Colors.white),
          padding: MaterialStateProperty.all(
            const EdgeInsets.symmetric(
              horizontal: spacingL,
              vertical: spacingM,
            ),
          ),
          shape: MaterialStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadiusL),
            ),
          ),
          elevation: MaterialStateProperty.all(0),
          overlayColor: MaterialStateProperty.resolveWith<Color>(
            (Set<MaterialState> states) {
              if (states.contains(MaterialState.pressed)) {
                return Colors.white.withOpacity(0.1);
              }
              return Colors.transparent;
            },
          ),
          textStyle: MaterialStateProperty.all(
            GoogleFonts.inter(
              fontSize: fontSizeS,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.3,
            ),
          ),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: ButtonStyle(
          foregroundColor: MaterialStateProperty.resolveWith<Color>(
            (Set<MaterialState> states) {
              if (states.contains(MaterialState.disabled)) {
                return primaryColor.withOpacity(0.5);
              }
              if (states.contains(MaterialState.hovered)) {
                return primaryColor.withOpacity(0.8);
              }
              return primaryColor;
            },
          ),
          side: MaterialStateProperty.resolveWith<BorderSide>(
            (Set<MaterialState> states) {
              if (states.contains(MaterialState.disabled)) {
                return BorderSide(color: primaryColor.withOpacity(0.5));
              }
              return BorderSide(color: primaryColor);
            },
          ),
          padding: MaterialStateProperty.all(
            const EdgeInsets.symmetric(
              horizontal: spacingL,
              vertical: spacingM,
            ),
          ),
          shape: MaterialStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadiusL),
            ),
          ),
          backgroundColor: MaterialStateProperty.resolveWith<Color>(
            (Set<MaterialState> states) {
              if (states.contains(MaterialState.hovered)) {
                return primaryColor.withOpacity(0.05);
              }
              return Colors.transparent;
            },
          ),
          overlayColor: MaterialStateProperty.resolveWith<Color>(
            (Set<MaterialState> states) {
              if (states.contains(MaterialState.pressed)) {
                return primaryColor.withOpacity(0.1);
              }
              return Colors.transparent;
            },
          ),
          textStyle: MaterialStateProperty.all(
            GoogleFonts.inter(
              fontSize: fontSizeS,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.3,
            ),
          ),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: ButtonStyle(
          foregroundColor: MaterialStateProperty.resolveWith<Color>(
            (Set<MaterialState> states) {
              if (states.contains(MaterialState.disabled)) {
                return primaryColor.withOpacity(0.5);
              }
              if (states.contains(MaterialState.hovered)) {
                return primaryColor.withOpacity(0.8);
              }
              return primaryColor;
            },
          ),
          padding: MaterialStateProperty.all(
            const EdgeInsets.symmetric(
              horizontal: spacingM,
              vertical: spacingS,
            ),
          ),
          overlayColor: MaterialStateProperty.resolveWith<Color>(
            (Set<MaterialState> states) {
              if (states.contains(MaterialState.hovered)) {
                return primaryColor.withOpacity(0.05);
              }
              if (states.contains(MaterialState.pressed)) {
                return primaryColor.withOpacity(0.1);
              }
              return Colors.transparent;
            },
          ),
          textStyle: MaterialStateProperty.all(
            GoogleFonts.inter(
              fontSize: fontSizeS,
              fontWeight: FontWeight.w500,
              letterSpacing: 0.3,
            ),
          ),
        ),
      ),

      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: componentBackColor,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: spacingM,
          vertical: spacingM,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusL),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusL),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusL),
          borderSide: const BorderSide(color: primaryColor, width: 1.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusL),
          borderSide: const BorderSide(color: errorColor),
        ),
        hintStyle: GoogleFonts.inter(
          fontSize: fontSizeS,
          color: textLight,
          fontWeight: FontWeight.w400,
        ),
        labelStyle: GoogleFonts.inter(
          fontSize: fontSizeS,
          color: textSecondary,
          fontWeight: FontWeight.w500,
        ),
        floatingLabelStyle: GoogleFonts.inter(
          fontSize: fontSizeS,
          color: primaryColor,
          fontWeight: FontWeight.w500,
        ),
      ),

      dividerTheme: const DividerThemeData(
        color: dividerColor,
        thickness: 1,
        space: spacingM,
      ),

      // Other theme properties
      scaffoldBackgroundColor: backgroundColor,
      primaryColor: primaryColor,
      hintColor: textLight,
      disabledColor: textLight,
      dividerColor: dividerColor,
    );
  }
}
