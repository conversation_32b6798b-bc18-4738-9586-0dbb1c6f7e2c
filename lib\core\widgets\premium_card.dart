import 'package:flutter/material.dart';
import 'package:guest_posts/core/theme/app_theme.dart';

/// A premium styled card with consistent styling
class PremiumCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? elevation;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? width;
  final double? height;
  final VoidCallback? onTap;
  final bool hasShadow;

  const PremiumCard({
    Key? key,
    required this.child,
    this.padding,
    this.margin,
    this.elevation,
    this.borderRadius,
    this.backgroundColor,
    this.borderColor,
    this.width,
    this.height,
    this.onTap,
    this.hasShadow = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final cardBorderRadius =
        borderRadius ?? BorderRadius.circular(AppTheme.borderRadiusL);

    final cardDecoration = BoxDecoration(
      color: backgroundColor ?? AppTheme.surfaceColor,
      borderRadius: cardBorderRadius,
      border: Border.all(
        color: borderColor ?? AppTheme.borderColor,
      ),
      boxShadow: hasShadow
          ? [
              BoxShadow(
                color: AppTheme.cardShadow.withOpacity(0.08),
                blurRadius: 20,
                offset: const Offset(0, 8),
                spreadRadius: 2,
              ),
            ]
          : null,
    );

    final cardChild = Container(
      width: width,
      height: height,
      padding: padding ?? const EdgeInsets.all(AppTheme.spacingL),
      decoration: cardDecoration,
      child: child,
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: cardBorderRadius,
        child: cardChild,
      );
    }

    return Container(
      margin: margin ?? const EdgeInsets.all(AppTheme.spacingS),
      child: cardChild,
    );
  }
}
