// import 'dart:math';

// import 'package:flutter/material.dart';
// import 'package:flutter_animate/flutter_animate.dart';
// import 'package:go_router/go_router.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'dart:ui';

// class PremiumSidebar extends StatefulWidget {
//   final String currentRoute;
//   final bool isAdvertiser;

//   const PremiumSidebar({
//     Key? key,
//     required this.currentRoute,
//     required this.isAdvertiser,
//   }) : super(key: key);

//   @override
//   State<PremiumSidebar> createState() => _PremiumSidebarState();
// }

// class _PremiumSidebarState extends State<PremiumSidebar>
//     with SingleTickerProviderStateMixin {
//   String? hoveredMenuItem;
//   late AnimationController _animationController;
//   late Animation<double> _animation;

//   @override
//   void initState() {
//     super.initState();
//     _animationController = AnimationController(
//       duration: const Duration(seconds: 30),
//       vsync: this,
//     )..repeat();
//     _animation = CurvedAnimation(
//       parent: _animationController,
//       curve: Curves.easeInOut,
//     );
//   }

//   @override
//   void dispose() {
//     _animationController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Stack(
//       children: [
//         // Background gradient with animated overlay
//         AnimatedBuilder(
//           animation: _animation,
//           builder: (context, child) {
//             return Container(
//               width: 300,
//               decoration: BoxDecoration(
//                 gradient: LinearGradient(
//                   begin: Alignment.topLeft,
//                   end: Alignment.bottomRight,
//                   colors: [
//                     Color(0xFF111428),
//                     Color(0xFF1F2138),
//                   ],
//                   stops: [0.0, 1.0],
//                 ),
//               ),
//               child: CustomPaint(
//                 painter: PremiumBackgroundPainter(animation: _animation.value),
//               ),
//             );
//           },
//         ),

//         // Sidebar content with glass effect overlay
//         ClipRRect(
//           child: BackdropFilter(
//             filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
//             child: Container(
//               width: 300,
//               decoration: BoxDecoration(
//                 gradient: LinearGradient(
//                   begin: Alignment.topCenter,
//                   end: Alignment.bottomCenter,
//                   colors: [
//                     Colors.white.withOpacity(0.03),
//                     Colors.white.withOpacity(0.01),
//                   ],
//                 ),
//                 border: Border(
//                   right: BorderSide(
//                     color: Colors.white.withOpacity(0.08),
//                     width: 1,
//                   ),
//                 ),
//                 boxShadow: [
//                   BoxShadow(
//                     color: Color(0xFF6A3DE8).withOpacity(0.15),
//                     offset: Offset(5, 0),
//                     blurRadius: 25,
//                     spreadRadius: 0,
//                   ),
//                 ],
//               ),
//               child: Column(
//                 children: [
//                   _buildLogoSection(),
//                   SizedBox(height: 10),
//                   _buildSearchBar(),
//                   SizedBox(height: 20),
//                   Expanded(
//                     child: _buildMenuItems(context),
//                   ),
//                   _buildUserSection(),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildSearchBar() {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 25),
//       child: Container(
//         height: 48,
//         decoration: BoxDecoration(
//           color: Colors.white.withOpacity(0.07),
//           borderRadius: BorderRadius.circular(14),
//           border: Border.all(
//             color: Colors.white.withOpacity(0.1),
//             width: 1,
//           ),
//         ),
//         child: Row(
//           children: [
//             Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 14),
//               child: Icon(
//                 Icons.search,
//                 color: Colors.white60,
//                 size: 20,
//               ),
//             ),
//             Expanded(
//               child: TextField(
//                 style: TextStyle(
//                   color: Colors.white,
//                   fontSize: 14,
//                 ),
//                 decoration: InputDecoration(
//                   hintText: 'Search...',
//                   hintStyle: TextStyle(
//                     color: Colors.white38,
//                     fontSize: 14,
//                     fontWeight: FontWeight.w400,
//                   ),
//                   border: InputBorder.none,
//                   contentPadding: EdgeInsets.symmetric(vertical: 14),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     )
//         .animate()
//         .fadeIn(delay: 400.ms, duration: 800.ms)
//         .slideY(begin: 0.2, end: 0);
//   }

//   Widget _buildLogoSection() {
//     return Container(
//       padding: EdgeInsets.only(top: 32, bottom: 20),
//       child: Column(
//         children: [
//           // 3D effect logo container with enhanced gradient
//           Container(
//             padding: EdgeInsets.all(18),
//             margin: EdgeInsets.symmetric(horizontal: 40),
//             decoration: BoxDecoration(
//               gradient: LinearGradient(
//                 colors: [
//                   Color(0xFF8A3CFF),
//                   Color(0xFF4E6FFF),
//                 ],
//                 begin: Alignment.topLeft,
//                 end: Alignment.bottomRight,
//               ),
//               borderRadius: BorderRadius.circular(18),
//               boxShadow: [
//                 BoxShadow(
//                   color: Color(0xFF8A3CFF).withOpacity(0.45),
//                   offset: Offset(0, 8),
//                   blurRadius: 24,
//                   spreadRadius: -4,
//                 ),
//                 BoxShadow(
//                   color: Colors.white.withOpacity(0.15),
//                   offset: Offset(0, -1),
//                   blurRadius: 2,
//                   spreadRadius: 0,
//                 ),
//               ],
//             ),
//             child: ShaderMask(
//               shaderCallback: (bounds) => LinearGradient(
//                 colors: [
//                   Colors.white.withOpacity(0.9),
//                   Colors.white,
//                 ],
//                 begin: Alignment.topLeft,
//                 end: Alignment.bottomRight,
//               ).createShader(bounds),
//               child: Icon(
//                 Icons.ads_click,
//                 color: Colors.white,
//                 size: 38,
//               ),
//             ),
//           )
//               .animate()
//               .fadeIn(duration: 800.ms)
//               .slideY(begin: -0.2, end: 0)
//               .then()
//               .shimmer(duration: 1500.ms, delay: 1000.ms),

//           SizedBox(height: 18),

//           // Enhanced brand name with better gradient text
//           ShaderMask(
//             shaderCallback: (bounds) => LinearGradient(
//               colors: [
//                 Color(0xFFAF8CFF),
//                 Color(0xFF7CB0FF),
//               ],
//               begin: Alignment.topLeft,
//               end: Alignment.bottomRight,
//             ).createShader(bounds),
//             child: Text(
//               "AdClone",
//               style: GoogleFonts.poppins(
//                 fontSize: 26,
//                 fontWeight: FontWeight.bold,
//                 color: Colors.white,
//                 letterSpacing: 0.5,
//                 height: 1,
//               ),
//             ),
//           ).animate().fadeIn(delay: 200.ms).slideY(begin: 0.2, end: 0),

//           SizedBox(height: 8),

//           // Subtitle with premium pill
//           Container(
//             padding: EdgeInsets.symmetric(horizontal: 14, vertical: 5),
//             decoration: BoxDecoration(
//               color: Colors.white.withOpacity(0.08),
//               borderRadius: BorderRadius.circular(30),
//               border: Border.all(
//                 color: Colors.white.withOpacity(0.1),
//                 width: 1,
//               ),
//             ),
//             child: Row(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 Container(
//                   width: 8,
//                   height: 8,
//                   decoration: BoxDecoration(
//                     color: Color(0xFF4CD964),
//                     shape: BoxShape.circle,
//                     boxShadow: [
//                       BoxShadow(
//                         color: Color(0xFF4CD964).withOpacity(0.4),
//                         blurRadius: 6,
//                         spreadRadius: 0,
//                       ),
//                     ],
//                   ),
//                 ),
//                 SizedBox(width: 8),
//                 Text(
//                   widget.isAdvertiser
//                       ? "Advertiser Portal"
//                       : "Publisher Portal",
//                   style: GoogleFonts.poppins(
//                     fontSize: 12,
//                     color: Colors.white70,
//                     fontWeight: FontWeight.w500,
//                     letterSpacing: 0.3,
//                   ),
//                 ),
//               ],
//             ),
//           )
//               .animate()
//               .fadeIn(delay: 400.ms)
//               .scale(begin: Offset(0.9, 0.9), end: Offset(1, 1)),
//         ],
//       ),
//     );
//   }

//   Widget _buildMenuItems(BuildContext context) {
//     List<Map<String, dynamic>> menuItems = widget.isAdvertiser
//         ? [
//             {
//               'title': 'Dashboard',
//               'icon': Icons.dashboard_rounded,
//               'route': '/advertiser/dashboard',
//               'notifications': 0,
//             },
//             {
//               'title': 'Create Campaign',
//               'icon': Icons.add_circle_rounded,
//               'route': '/advertiser/create-campaign',
//               'notifications': 0,
//             },
//             {
//               'title': 'Active Campaigns',
//               'icon': Icons.campaign_rounded,
//               'route': '/advertiser/campaigns',
//               'notifications': 3,
//             },
//             {
//               'title': 'Analytics',
//               'icon': Icons.analytics_rounded,
//               'route': '/advertiser/analytics',
//               'notifications': 0,
//             },
//             {
//               'title': 'Billing',
//               'icon': Icons.account_balance_wallet_rounded,
//               'route': '/advertiser/billing',
//               'notifications': 1,
//             },
//           ]
//         : [
//             {
//               'title': 'Dashboard',
//               'icon': Icons.dashboard_rounded,
//               'route': '/publisher/dashboard',
//               'notifications': 0,
//             },
//             {
//               'title': 'Available Campaigns',
//               'icon': Icons.list_alt_rounded,
//               'route': '/publisher/campaigns',
//               'notifications': 5,
//             },
//             {
//               'title': 'My Ads',
//               'icon': Icons.ads_click_rounded,
//               'route': '/publisher/my-ads',
//               'notifications': 0,
//             },
//             {
//               'title': 'Earnings',
//               'icon': Icons.attach_money_rounded,
//               'route': '/publisher/earnings',
//               'notifications': 2,
//             },
//             {
//               'title': 'Account',
//               'icon': Icons.person_rounded,
//               'route': '/publisher/account',
//               'notifications': 0,
//             },
//           ];

//     return SingleChildScrollView(
//       physics: BouncingScrollPhysics(),
//       child: Column(
//         children: [
//           _buildSectionTitle("MENU"),
//           ...menuItems.map((item) => _buildMenuItem(
//                 context,
//                 title: item['title'],
//                 icon: item['icon'],
//                 route: item['route'],
//                 isActive: widget.currentRoute == item['route'],
//                 index: menuItems.indexOf(item),
//                 notifications: item['notifications'],
//               )),
//           SizedBox(height: 20),
//           _buildSectionTitle("SETTINGS"),
//           _buildMenuItem(
//             context,
//             title: "Settings",
//             icon: Icons.settings_rounded,
//             route: "/settings",
//             isActive: false,
//             index: menuItems.length,
//             notifications: 0,
//           ),
//           _buildMenuItem(
//             context,
//             title: "Help & Support",
//             icon: Icons.help_outline_rounded,
//             route: "/support",
//             isActive: false,
//             index: menuItems.length + 1,
//             notifications: 0,
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildSectionTitle(String title) {
//     return Padding(
//       padding: EdgeInsets.symmetric(horizontal: 28, vertical: 14),
//       child: Row(
//         children: [
//           Text(
//             title,
//             style: GoogleFonts.poppins(
//               color: Colors.white38,
//               fontSize: 11,
//               fontWeight: FontWeight.w600,
//               letterSpacing: 1.8,
//             ),
//           ),
//           SizedBox(width: 12),
//           Expanded(
//             child: Container(
//               height: 1,
//               decoration: BoxDecoration(
//                 gradient: LinearGradient(
//                   colors: [
//                     Colors.white.withOpacity(0.08),
//                     Colors.white.withOpacity(0.01),
//                   ],
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildMenuItem(
//     BuildContext context, {
//     required String title,
//     required IconData icon,
//     required String route,
//     required bool isActive,
//     required int index,
//     required int notifications,
//   }) {
//     bool isHovered = hoveredMenuItem == title;

//     return MouseRegion(
//       onEnter: (_) => setState(() => hoveredMenuItem = title),
//       onExit: (_) => setState(() => hoveredMenuItem = null),
//       child: AnimatedContainer(
//         duration: Duration(milliseconds: 300),
//         curve: Curves.easeOutQuint,
//         margin: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
//         decoration: BoxDecoration(
//           gradient: isActive
//               ? LinearGradient(
//                   colors: [
//                     Color(0xFF8A3CFF),
//                     Color(0xFF4E6FFF),
//                   ],
//                   begin: Alignment.centerLeft,
//                   end: Alignment.centerRight,
//                 )
//               : isHovered
//                   ? LinearGradient(
//                       colors: [
//                         Colors.white.withOpacity(0.05),
//                         Colors.white.withOpacity(0.02),
//                       ],
//                       begin: Alignment.centerLeft,
//                       end: Alignment.centerRight,
//                     )
//                   : null,
//           borderRadius: BorderRadius.circular(14),
//           border: Border.all(
//             color: isActive
//                 ? Colors.white.withOpacity(0.2)
//                 : isHovered
//                     ? Colors.white.withOpacity(0.08)
//                     : Colors.transparent,
//             width: 1,
//           ),
//           boxShadow: isActive
//               ? [
//                   BoxShadow(
//                     color: Color(0xFF8A3CFF).withOpacity(0.4),
//                     offset: Offset(0, 6),
//                     blurRadius: 16,
//                     spreadRadius: -4,
//                   )
//                 ]
//               : null,
//         ),
//         child: Material(
//           color: Colors.transparent,
//           child: InkWell(
//             onTap: () => context.go(route),
//             borderRadius: BorderRadius.circular(14),
//             splashColor: Colors.white.withOpacity(0.05),
//             highlightColor: Colors.white.withOpacity(0.05),
//             child: Padding(
//               padding: EdgeInsets.symmetric(horizontal: 18, vertical: 13),
//               child: Row(
//                 children: [
//                   // Icon Container
//                   Container(
//                     padding: EdgeInsets.all(10),
//                     decoration: BoxDecoration(
//                       color: isActive
//                           ? Colors.white.withOpacity(0.15)
//                           : isHovered
//                               ? Colors.white.withOpacity(0.05)
//                               : Colors.white.withOpacity(0.03),
//                       borderRadius: BorderRadius.circular(12),
//                       border: Border.all(
//                         color: isActive
//                             ? Colors.white.withOpacity(0.2)
//                             : Colors.white.withOpacity(0.05),
//                         width: 1,
//                       ),
//                       boxShadow: isActive
//                           ? [
//                               BoxShadow(
//                                 color: Colors.black.withOpacity(0.2),
//                                 offset: Offset(0, 2),
//                                 blurRadius: 4,
//                                 spreadRadius: -1,
//                               )
//                             ]
//                           : null,
//                     ),
//                     child: Icon(
//                       icon,
//                       size: 20,
//                       color: isActive
//                           ? Colors.white
//                           : isHovered
//                               ? Colors.white70
//                               : Colors.white38,
//                     ),
//                   ),
//                   SizedBox(width: 14),

//                   // Menu Title
//                   Expanded(
//                     child: Text(
//                       title,
//                       style: GoogleFonts.poppins(
//                         color: isActive
//                             ? Colors.white
//                             : isHovered
//                                 ? Colors.white70
//                                 : Colors.white38,
//                         fontSize: 14,
//                         fontWeight: isActive || isHovered
//                             ? FontWeight.w600
//                             : FontWeight.w500,
//                         letterSpacing: 0.2,
//                       ),
//                     ),
//                   ),

//                   if (notifications > 0) ...[
//                     Container(
//                       padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
//                       decoration: BoxDecoration(
//                         color: isActive
//                             ? Colors.white.withOpacity(0.2)
//                             : Color(0xFFFF3B30).withOpacity(0.9),
//                         borderRadius: BorderRadius.circular(10),
//                         boxShadow: [
//                           BoxShadow(
//                             color: isActive
//                                 ? Colors.black.withOpacity(0.2)
//                                 : Color(0xFFFF3B30).withOpacity(0.3),
//                             blurRadius: 8,
//                             spreadRadius: -1,
//                           ),
//                         ],
//                       ),
//                       child: Text(
//                         notifications.toString(),
//                         style: GoogleFonts.poppins(
//                           color: Colors.white,
//                           fontSize: 10,
//                           fontWeight: FontWeight.w600,
//                         ),
//                       ),
//                     ),
//                   ],

//                   if (isActive) ...[
//                     if (notifications == 0) SizedBox(width: 8),
//                     Container(
//                       width: 6,
//                       height: 6,
//                       decoration: BoxDecoration(
//                         color: Colors.white,
//                         shape: BoxShape.circle,
//                         boxShadow: [
//                           BoxShadow(
//                             color: Colors.white.withOpacity(0.5),
//                             blurRadius: 4,
//                           ),
//                         ],
//                       ),
//                     ),
//                   ],
//                 ],
//               ),
//             ),
//           ),
//         ),
//       ),
//     )
//         .animate(delay: (100 * index).ms)
//         .fadeIn(duration: 400.ms)
//         .slideX(begin: -0.1, end: 0);
//   }

//   Widget _buildUserSection() {
//     return Container(
//       margin: EdgeInsets.all(16),
//       padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
//       decoration: BoxDecoration(
//         color: Colors.white.withOpacity(0.03),
//         borderRadius: BorderRadius.circular(18),
//         border: Border.all(
//           color: Colors.white.withOpacity(0.06),
//           width: 1,
//         ),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.2),
//             blurRadius: 10,
//             spreadRadius: -2,
//           ),
//         ],
//       ),
//       child: Row(
//         children: [
//           // Premium indicator and profile image stack
//           Stack(
//             children: [
//               // Profile image with enhanced shadow
//               Container(
//                 decoration: BoxDecoration(
//                   shape: BoxShape.circle,
//                   boxShadow: [
//                     BoxShadow(
//                       color: Color(0xFF8A3CFF).withOpacity(0.3),
//                       blurRadius: 16,
//                       spreadRadius: -2,
//                     ),
//                   ],
//                 ),
//                 child: CircleAvatar(
//                   radius: 22,
//                   backgroundImage: NetworkImage(
//                       'https://randomuser.me/api/portraits/men/32.jpg'),
//                 ),
//               ),

//               // Premium indicator badge
//               Positioned(
//                 right: 0,
//                 bottom: 0,
//                 child: Container(
//                   padding: EdgeInsets.all(4),
//                   decoration: BoxDecoration(
//                     color: Color(0xFFFFC107),
//                     shape: BoxShape.circle,
//                     border: Border.all(
//                       color: Color(0xFF1F2138),
//                       width: 2,
//                     ),
//                     boxShadow: [
//                       BoxShadow(
//                         color: Color(0xFFFFC107).withOpacity(0.4),
//                         blurRadius: 8,
//                         spreadRadius: -1,
//                       ),
//                     ],
//                   ),
//                   child: Icon(
//                     Icons.star,
//                     color: Colors.white,
//                     size: 10,
//                   ),
//                 ),
//               ),
//             ],
//           ),
//           SizedBox(width: 14),

//           // User Info with enhanced typography
//           Expanded(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   'John Smith',
//                   style: GoogleFonts.poppins(
//                     fontWeight: FontWeight.w600,
//                     fontSize: 14,
//                     color: Colors.white,
//                     letterSpacing: 0.2,
//                   ),
//                 ),
//                 SizedBox(height: 3),
//                 Row(
//                   children: [
//                     Container(
//                       width: 8,
//                       height: 8,
//                       decoration: BoxDecoration(
//                         color: Color(0xFF4CD964),
//                         shape: BoxShape.circle,
//                         boxShadow: [
//                           BoxShadow(
//                             color: Color(0xFF4CD964).withOpacity(0.4),
//                             blurRadius: 6,
//                             spreadRadius: 0,
//                           ),
//                         ],
//                       ),
//                     ),
//                     SizedBox(width: 6),
//                     Text(
//                       'Premium',
//                       style: GoogleFonts.poppins(
//                         color: Colors.white60,
//                         fontSize: 12,
//                         fontWeight: FontWeight.w500,
//                       ),
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//           ),

//           // Logout Button with hover effect
//           MouseRegion(
//             cursor: SystemMouseCursors.click,
//             child: Container(
//               decoration: BoxDecoration(
//                 color: Colors.white.withOpacity(0.05),
//                 borderRadius: BorderRadius.circular(10),
//                 border: Border.all(
//                   color: Colors.white.withOpacity(0.08),
//                   width: 1,
//                 ),
//               ),
//               child: IconButton(
//                 onPressed: () {},
//                 icon: Icon(
//                   Icons.logout_rounded,
//                   color: Colors.white60,
//                   size: 18,
//                 ),
//                 tooltip: 'Logout',
//                 splashRadius: 20,
//                 hoverColor: Colors.white.withOpacity(0.1),
//               ),
//             ),
//           ),
//         ],
//       ),
//     ).animate().fadeIn(delay: 800.ms).slideY(begin: 0.2, end: 0);
//   }
// }

// // Premium background painter for animated gradients and patterns
// class PremiumBackgroundPainter extends CustomPainter {
//   final double animation;

//   PremiumBackgroundPainter({required this.animation});

//   @override
//   void paint(Canvas canvas, Size size) {
//     // Paint for subtle decorative elements
//     final paint = Paint()
//       ..color = Colors.white.withOpacity(0.03)
//       ..strokeWidth = 1
//       ..style = PaintingStyle.stroke;

//     // Create subtle moving wave patterns
//     for (int i = 0; i < 8; i++) {
//       double offsetY =
//           size.height * 0.1 * i + (size.height * 0.2 * animation) % size.height;

//       Path path = Path();
//       path.moveTo(0, offsetY);

//       for (double x = 0; x < size.width; x += size.width / 20) {
//         double y = offsetY +
//             sin((x / size.width) * 2 * 3.14 + animation * 2 * 3.14) * 10;
//         path.lineTo(x, y);
//       }

//       canvas.drawPath(path, paint);
//     }

//     // Add some decorative dots
//     final dotPaint = Paint()
//       ..color = Colors.white.withOpacity(0.04)
//       ..style = PaintingStyle.fill;

//     for (int i = 0; i < 30; i++) {
//       double x = (i * 37 + animation * 50) % size.width;
//       double y = (i * 53 + animation * 30) % size.height;
//       double radius = 1 + (i % 3);

//       canvas.drawCircle(Offset(x, y), radius, dotPaint);
//     }
//   }

//   @override
//   bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
// }

// class AppTheme {
//   // Premium gradient colors - enhanced
//   static const Color primaryPurple = Color(0xFF8A3CFF);
//   static const Color primaryBlue = Color(0xFF4E6FFF);
//   static const Color accentColor = Color(0xFF8A3DE8);
//   static const Color darkBackground = Color(0xFF111428);
//   static const Color darkBackgroundSecondary = Color(0xFF1F2138);

//   static const LinearGradient primaryGradient = LinearGradient(
//     colors: [primaryPurple, primaryBlue],
//     begin: Alignment.topLeft,
//     end: Alignment.bottomRight,
//   );

//   static const LinearGradient buttonGradient = LinearGradient(
//     colors: [Color(0xFF8A3CFF), Color(0xFF4E6FFF)],
//     begin: Alignment.centerLeft,
//     end: Alignment.centerRight,
//   );

//   static ThemeData darkTheme = ThemeData(
//     useMaterial3: true,
//     colorScheme: ColorScheme.dark(
//       primary: primaryPurple,
//       secondary: accentColor,
//       onPrimary: Colors.white,
//       background: darkBackground,
//       surface: darkBackgroundSecondary,
//     ),
//     textTheme: GoogleFonts.poppinsTextTheme(ThemeData.dark().textTheme),
//     elevatedButtonTheme: ElevatedButtonThemeData(
//       style: ElevatedButton.styleFrom(
//         padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(12),
//         ),
//       ),
//     ),
//     inputDecorationTheme: InputDecorationTheme(
//       filled: true,
//       fillColor: Colors.white.withOpacity(0.05),
//       border: OutlineInputBorder(
//         borderRadius: BorderRadius.circular(14),
//         borderSide: BorderSide(color: Colors.white.withOpacity(0.1), width: 1),
//       ),
//       focusedBorder: OutlineInputBorder(
//         borderRadius: BorderRadius.circular(14),
//         borderSide: BorderSide(color: primaryPurple, width: 2),
//       ),
//       enabledBorder: OutlineInputBorder(
//         borderRadius: BorderRadius.circular(14),
//         borderSide: BorderSide(color: Colors.white.withOpacity(0.1), width: 1),
//       ),
//     ),
//     cardTheme: CardTheme(
//       elevation: 8,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(18),
//       ),
//       color: darkBackgroundSecondary,
//     ),
//     scaffoldBackgroundColor: darkBackground,
//   );
// }
