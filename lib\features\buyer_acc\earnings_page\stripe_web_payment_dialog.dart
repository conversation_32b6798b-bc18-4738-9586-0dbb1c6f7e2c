// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:cloud_functions/cloud_functions.dart';
// import 'package:guest_posts/core/theme/app_theme.dart';
// import 'package:font_awesome_flutter/font_awesome_flutter.dart';
// import 'package:guest_posts/features/buyer_acc/earnings_page/expiry_date_formatter.dart';

// class StripeWebPaymentDialog extends StatefulWidget {
//   final double amount;
//   final String currency;
//   final Function(String paymentId) onPaymentSuccess;
//   final Function(String error) onPaymentError;
//   final Function() onPaymentCancelled;

//   const StripeWebPaymentDialog({
//     super.key,
//     required this.amount,
//     this.currency = 'USD',
//     required this.onPaymentSuccess,
//     required this.onPaymentError,
//     required this.onPaymentCancelled,
//   });

//   @override
//   State<StripeWebPaymentDialog> createState() => _StripeWebPaymentDialogState();
// }

// class _StripeWebPaymentDialogState extends State<StripeWebPaymentDialog> {
//   bool _isLoading = false;
//   String _errorMessage = '';
//   Map<String, dynamic>? _paymentIntent;
//   bool _isFormValid = false;

//   // Text controllers
//   final TextEditingController _cardNumberController = TextEditingController();
//   final TextEditingController _expiryDateController = TextEditingController();
//   final TextEditingController _cvcController = TextEditingController();
//   final TextEditingController _nameController = TextEditingController();

//   // Validation state
//   String? _cardNumberError;
//   String? _expiryDateError;
//   String? _cvcError;
//   String? _nameError;

//   // Input formatters
//   final _cardNumberFormatter = FilteringTextInputFormatter.digitsOnly;
//   final _expiryDateFormatter = ExpiryDateInputFormatter();
//   final _cvcFormatter = FilteringTextInputFormatter.digitsOnly;

//   @override
//   void initState() {
//     super.initState();
//     _initializePayment();

//     // Add listeners to validate on change
//     _cardNumberController.addListener(_validateForm);
//     _expiryDateController.addListener(_validateForm);
//     _cvcController.addListener(_validateForm);
//     _nameController.addListener(_validateForm);
//   }

//   @override
//   void dispose() {
//     _cardNumberController.dispose();
//     _expiryDateController.dispose();
//     _cvcController.dispose();
//     _nameController.dispose();
//     super.dispose();
//   }

//   // Validate all form fields and update state
//   void _validateForm() {
//     _validateCardNumber();
//     _validateExpiryDate();
//     _validateCVC();
//     _validateName();

//     setState(() {
//       _isFormValid = _cardNumberError == null &&
//           _expiryDateError == null &&
//           _cvcError == null &&
//           _nameError == null &&
//           _cardNumberController.text.isNotEmpty &&
//           _expiryDateController.text.isNotEmpty &&
//           _cvcController.text.isNotEmpty &&
//           _nameController.text.isNotEmpty;
//     });
//   }

//   // Validate card number using Luhn algorithm
//   void _validateCardNumber() {
//     final cardNumber =
//         _cardNumberController.text.replaceAll(RegExp(r'\s+'), '');

//     if (cardNumber.isEmpty) {
//       setState(() => _cardNumberError = 'Card number is required');
//       return;
//     }

//     if (cardNumber.length != 16) {
//       setState(() => _cardNumberError = 'Card number must be 16 digits');
//       return;
//     }

//     if (!_isValidLuhn(cardNumber)) {
//       setState(() => _cardNumberError = 'Invalid card number');
//       return;
//     }

//     setState(() => _cardNumberError = null);
//   }

//   // Luhn algorithm implementation
//   bool _isValidLuhn(String number) {
//     int sum = 0;
//     bool alternate = false;

//     for (int i = number.length - 1; i >= 0; i--) {
//       int digit = int.parse(number[i]);

//       if (alternate) {
//         digit *= 2;
//         if (digit > 9) {
//           digit = (digit % 10) + 1;
//         }
//       }

//       sum += digit;
//       alternate = !alternate;
//     }

//     return sum % 10 == 0;
//   }

//   // Validate expiry date
//   void _validateExpiryDate() {
//     final expiryDate = _expiryDateController.text;

//     if (expiryDate.isEmpty) {
//       setState(() => _expiryDateError = 'Expiry date is required');
//       return;
//     }

//     // Check format
//     if (!RegExp(r'^\d{2}/\d{2}$').hasMatch(expiryDate)) {
//       setState(() => _expiryDateError = 'Use MM/YY format');
//       return;
//     }

//     // Parse month and year
//     final parts = expiryDate.split('/');
//     final month = int.tryParse(parts[0]);
//     final year = int.tryParse(parts[1]);

//     if (month == null || year == null || month < 1 || month > 12) {
//       setState(() => _expiryDateError = 'Invalid month');
//       return;
//     }

//     // Check if card is expired
//     final now = DateTime.now();
//     final cardYear = 2000 + year; // Convert YY to 20YY
//     final cardExpiryDate =
//         DateTime(cardYear, month + 1, 0); // Last day of expiry month

//     if (cardExpiryDate.isBefore(now)) {
//       setState(() => _expiryDateError = 'Card has expired');
//       return;
//     }

//     setState(() => _expiryDateError = null);
//   }

//   // Validate CVC
//   void _validateCVC() {
//     final cvc = _cvcController.text;

//     if (cvc.isEmpty) {
//       setState(() => _cvcError = 'CVC is required');
//       return;
//     }

//     if (cvc.length < 3 || cvc.length > 4) {
//       setState(() => _cvcError = 'CVC must be 3-4 digits');
//       return;
//     }

//     if (!RegExp(r'^\d+$').hasMatch(cvc)) {
//       setState(() => _cvcError = 'CVC must contain only digits');
//       return;
//     }

//     setState(() => _cvcError = null);
//   }

//   // Validate name
//   void _validateName() {
//     final name = _nameController.text;

//     if (name.isEmpty) {
//       setState(() => _nameError = 'Name is required');
//       return;
//     }

//     if (name.length < 3) {
//       setState(() => _nameError = 'Name is too short');
//       return;
//     }

//     if (!RegExp(r"^[a-zA-Z\s\-\'\.]+$").hasMatch(name)) {
//       setState(() => _nameError = 'Name contains invalid characters');
//       return;
//     }

//     setState(() => _nameError = null);
//   }

//   Future<void> _initializePayment() async {
//     setState(() {
//       _isLoading = true;
//       _errorMessage = '';
//     });

//     try {
//       // Create payment intent on the server
//       final callable =
//           FirebaseFunctions.instance.httpsCallable('createStripePaymentIntent');
//       final response = await callable.call({
//         'amount': (widget.amount * 100).toInt(), // Convert to cents
//         'currency': widget.currency,
//       });

//       setState(() {
//         _paymentIntent = response.data;
//         _isLoading = false;
//       });
//     } catch (e) {
//       setState(() {
//         _isLoading = false;
//         _errorMessage = 'Failed to initialize payment: ${e.toString()}';
//       });
//       widget.onPaymentError(_errorMessage);
//     }
//   }

//   Future<void> _handlePayment() async {
//     if (_paymentIntent == null) {
//       setState(() {
//         _errorMessage = 'Payment not initialized';
//       });
//       return;
//     }

//     // Validate all fields before proceeding
//     _validateForm();

//     if (!_isFormValid) {
//       setState(() {
//         _errorMessage = 'Please correct the errors in the form';
//       });
//       return;
//     }

//     setState(() {
//       _isLoading = true;
//       _errorMessage = '';
//     });

//     try {
//       // In a real implementation, you would use Stripe.js to handle the payment
//       // For this example, we'll simulate a successful payment
//       await Future.delayed(const Duration(seconds: 2));

//       // Simulate a successful payment
//       widget.onPaymentSuccess(_paymentIntent!['id']);
//       if (mounted) {
//         Navigator.of(context).pop();
//       }
//     } catch (e) {
//       setState(() {
//         _isLoading = false;
//         _errorMessage = 'Payment error: ${e.toString()}';
//       });
//       widget.onPaymentError(_errorMessage);
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Dialog(
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
//       elevation: 0,
//       backgroundColor: Colors.transparent,
//       child: contentBox(context),
//     );
//   }

//   Widget contentBox(BuildContext context) {
//     return Container(
//       padding: const EdgeInsets.all(20),
//       width: 450,
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(20),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.1),
//             spreadRadius: 5,
//             blurRadius: 15,
//             offset: const Offset(0, 5),
//           ),
//         ],
//       ),
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           // Header
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Row(
//                 children: [
//                   Container(
//                     padding: const EdgeInsets.all(10),
//                     decoration: BoxDecoration(
//                       color: AppTheme.accentColor.withOpacity(0.1),
//                       borderRadius: BorderRadius.circular(12),
//                     ),
//                     child: Icon(
//                       FontAwesomeIcons.creditCard,
//                       color: AppTheme.accentColor,
//                       size: 20,
//                     ),
//                   ),
//                   const SizedBox(width: 15),
//                   const Text(
//                     'Stripe Payment',
//                     style: TextStyle(
//                       fontSize: 22,
//                       fontWeight: FontWeight.bold,
//                       fontFamily: 'Cairo',
//                     ),
//                   ),
//                 ],
//               ),
//               IconButton(
//                 icon: const Icon(Icons.close, color: Colors.grey),
//                 onPressed: () {
//                   widget.onPaymentCancelled();
//                   Navigator.of(context).pop();
//                 },
//               ),
//             ],
//           ),
//           const SizedBox(height: 20),

//           // Amount display
//           Container(
//             padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
//             decoration: BoxDecoration(
//               color: Colors.grey.shade50,
//               borderRadius: BorderRadius.circular(15),
//               border: Border.all(color: Colors.grey.shade200),
//             ),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 const Text(
//                   'Amount:',
//                   style: TextStyle(
//                     fontSize: 16,
//                     fontWeight: FontWeight.w500,
//                     fontFamily: 'Cairo',
//                   ),
//                 ),
//                 Text(
//                   '${widget.currency} ${widget.amount.toStringAsFixed(2)}',
//                   style: TextStyle(
//                     fontSize: 20,
//                     fontWeight: FontWeight.bold,
//                     color: AppTheme.accentColor,
//                     fontFamily: 'Cairo',
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           const SizedBox(height: 25),

//           // Card input
//           if (_isLoading)
//             const Center(
//               child: CircularProgressIndicator(),
//             )
//           else if (_errorMessage.isNotEmpty)
//             Container(
//               padding: const EdgeInsets.all(15),
//               decoration: BoxDecoration(
//                 color: Colors.red.shade50,
//                 borderRadius: BorderRadius.circular(10),
//                 border: Border.all(color: Colors.red.shade200),
//               ),
//               child: Row(
//                 children: [
//                   Icon(Icons.error_outline, color: Colors.red.shade700),
//                   const SizedBox(width: 10),
//                   Expanded(
//                     child: Text(
//                       _errorMessage,
//                       style: TextStyle(color: Colors.red.shade700),
//                     ),
//                   ),
//                 ],
//               ),
//             )
//           else
//             Column(
//               children: [
//                 // Card number
//                 TextField(
//                   controller: _cardNumberController,
//                   decoration: InputDecoration(
//                     labelText: 'Card Number',
//                     hintText: '4242 4242 4242 4242',
//                     border: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(12),
//                     ),
//                     prefixIcon: const Icon(Icons.credit_card),
//                     errorText: _cardNumberError,
//                   ),
//                   keyboardType: TextInputType.number,
//                   inputFormatters: [
//                     FilteringTextInputFormatter.digitsOnly,
//                     LengthLimitingTextInputFormatter(16),
//                   ],
//                 ),
//                 const SizedBox(height: 15),

//                 // Expiry date and CVC
//                 Row(
//                   children: [
//                     Expanded(
//                       child: TextField(
//                         controller: _expiryDateController,
//                         decoration: InputDecoration(
//                           labelText: 'Expiry Date',
//                           hintText: 'MM/YY',
//                           border: OutlineInputBorder(
//                             borderRadius: BorderRadius.circular(12),
//                           ),
//                           errorText: _expiryDateError,
//                         ),
//                         inputFormatters: [
//                           _expiryDateFormatter,
//                         ],
//                       ),
//                     ),
//                     const SizedBox(width: 15),
//                     Expanded(
//                       child: TextField(
//                         controller: _cvcController,
//                         decoration: InputDecoration(
//                           labelText: 'CVC',
//                           hintText: '123',
//                           border: OutlineInputBorder(
//                             borderRadius: BorderRadius.circular(12),
//                           ),
//                           errorText: _cvcError,
//                         ),
//                         keyboardType: TextInputType.number,
//                         inputFormatters: [
//                           FilteringTextInputFormatter.digitsOnly,
//                           LengthLimitingTextInputFormatter(4),
//                         ],
//                       ),
//                     ),
//                   ],
//                 ),
//                 const SizedBox(height: 15),

//                 // Name on card
//                 TextField(
//                   controller: _nameController,
//                   decoration: InputDecoration(
//                     labelText: 'Name on Card',
//                     border: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(12),
//                     ),
//                     prefixIcon: const Icon(Icons.person),
//                     errorText: _nameError,
//                   ),
//                 ),
//                 const SizedBox(height: 20),

//                 // Pay button
//                 SizedBox(
//                   width: double.infinity,
//                   height: 50,
//                   child: ElevatedButton(
//                     onPressed: _isFormValid ? _handlePayment : null,
//                     style: ElevatedButton.styleFrom(
//                       backgroundColor: AppTheme.accentColor,
//                       foregroundColor: Colors.white,
//                       shape: RoundedRectangleBorder(
//                         borderRadius: BorderRadius.circular(12),
//                       ),
//                       elevation: 2,
//                       disabledBackgroundColor: Colors.grey.shade300,
//                     ),
//                     child: const Text(
//                       'Pay Now',
//                       style: TextStyle(
//                         fontSize: 16,
//                         fontWeight: FontWeight.bold,
//                         fontFamily: 'Cairo',
//                       ),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           const SizedBox(height: 20),

//           // Secure payment note
//           Row(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               Icon(
//                 Icons.lock,
//                 size: 16,
//                 color: Colors.grey.shade600,
//               ),
//               const SizedBox(width: 8),
//               Text(
//                 'Secure payment via Stripe',
//                 style: TextStyle(
//                   color: Colors.grey.shade600,
//                   fontSize: 14,
//                 ),
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }

//   Future<void> _initializePayment() async {
//     setState(() {
//       _isLoading = true;
//       _errorMessage = '';
//     });

//     try {
//       // Create payment intent on the server
//       final callable =
//           FirebaseFunctions.instance.httpsCallable('createStripePaymentIntent');
//       final response = await callable.call({
//         'amount': (widget.amount * 100).toInt(), // Convert to cents
//         'currency': widget.currency,
//       });

//       setState(() {
//         _paymentIntent = response.data;
//         _isLoading = false;
//       });
//     } catch (e) {
//       setState(() {
//         _isLoading = false;
//         _errorMessage = 'Failed to initialize payment: ${e.toString()}';
//       });
//       widget.onPaymentError(_errorMessage);
//     }
//   }

//   Future<void> _handlePayment() async {
//     if (_paymentIntent == null) {
//       setState(() {
//         _errorMessage = 'Payment not initialized';
//       });
//       return;
//     }

//     // Basic validation
//     if (_cardNumberController.text.isEmpty ||
//         _expiryDateController.text.isEmpty ||
//         _cvcController.text.isEmpty ||
//         _nameController.text.isEmpty) {
//       setState(() {
//         _errorMessage = 'Please fill in all card details';
//       });
//       return;
//     }

//     setState(() {
//       _isLoading = true;
//       _errorMessage = '';
//     });

//     try {
//       // In a real implementation, you would use Stripe.js to handle the payment
//       // For this example, we'll simulate a successful payment
//       await Future.delayed(const Duration(seconds: 2));

//       // Simulate a successful payment
//       widget.onPaymentSuccess(_paymentIntent!['id']);
//       if (mounted) {
//         Navigator.of(context).pop();
//       }
//     } catch (e) {
//       setState(() {
//         _isLoading = false;
//         _errorMessage = 'Payment error: ${e.toString()}';
//       });
//       widget.onPaymentError(_errorMessage);
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Dialog(
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
//       elevation: 0,
//       backgroundColor: Colors.transparent,
//       child: contentBox(context),
//     );
//   }

//   Widget contentBox(BuildContext context) {
//     return Container(
//       padding: const EdgeInsets.all(20),
//       width: 450,
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(20),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.1),
//             spreadRadius: 5,
//             blurRadius: 15,
//             offset: const Offset(0, 5),
//           ),
//         ],
//       ),
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           // Header
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Row(
//                 children: [
//                   Container(
//                     padding: const EdgeInsets.all(10),
//                     decoration: BoxDecoration(
//                       color: AppTheme.accentColor.withOpacity(0.1),
//                       borderRadius: BorderRadius.circular(12),
//                     ),
//                     child: Icon(
//                       FontAwesomeIcons.creditCard,
//                       color: AppTheme.accentColor,
//                       size: 20,
//                     ),
//                   ),
//                   const SizedBox(width: 15),
//                   const Text(
//                     'Stripe Payment',
//                     style: TextStyle(
//                       fontSize: 22,
//                       fontWeight: FontWeight.bold,
//                       fontFamily: 'Cairo',
//                     ),
//                   ),
//                 ],
//               ),
//               IconButton(
//                 icon: const Icon(Icons.close, color: Colors.grey),
//                 onPressed: () {
//                   widget.onPaymentCancelled();
//                   Navigator.of(context).pop();
//                 },
//               ),
//             ],
//           ),
//           const SizedBox(height: 20),

//           // Amount display
//           Container(
//             padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
//             decoration: BoxDecoration(
//               color: Colors.grey.shade50,
//               borderRadius: BorderRadius.circular(15),
//               border: Border.all(color: Colors.grey.shade200),
//             ),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 const Text(
//                   'Amount:',
//                   style: TextStyle(
//                     fontSize: 16,
//                     fontWeight: FontWeight.w500,
//                     fontFamily: 'Cairo',
//                   ),
//                 ),
//                 Text(
//                   '${widget.currency} ${widget.amount.toStringAsFixed(2)}',
//                   style: TextStyle(
//                     fontSize: 20,
//                     fontWeight: FontWeight.bold,
//                     color: AppTheme.accentColor,
//                     fontFamily: 'Cairo',
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           const SizedBox(height: 25),

//           // Card input
//           if (_isLoading)
//             const Center(
//               child: CircularProgressIndicator(),
//             )
//           else if (_errorMessage.isNotEmpty)
//             Container(
//               padding: const EdgeInsets.all(15),
//               decoration: BoxDecoration(
//                 color: Colors.red.shade50,
//                 borderRadius: BorderRadius.circular(10),
//                 border: Border.all(color: Colors.red.shade200),
//               ),
//               child: Row(
//                 children: [
//                   Icon(Icons.error_outline, color: Colors.red.shade700),
//                   const SizedBox(width: 10),
//                   Expanded(
//                     child: Text(
//                       _errorMessage,
//                       style: TextStyle(color: Colors.red.shade700),
//                     ),
//                   ),
//                 ],
//               ),
//             )
//           else
//             Column(
//               children: [
//                 // Card number
//                 TextField(
//                   controller: _cardNumberController,
//                   decoration: InputDecoration(
//                     labelText: 'Card Number',
//                     hintText: '4242 4242 4242 4242',
//                     border: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(12),
//                     ),
//                     prefixIcon: const Icon(Icons.credit_card),
//                   ),
//                   keyboardType: TextInputType.number,
//                 ),
//                 const SizedBox(height: 15),

//                 // Expiry date and CVC
//                 Row(
//                   children: [
//                     Expanded(
//                       child: TextField(
//                         controller: _expiryDateController,
//                         decoration: InputDecoration(
//                           labelText: 'Expiry Date',
//                           hintText: 'MM/YY',
//                           border: OutlineInputBorder(
//                             borderRadius: BorderRadius.circular(12),
//                           ),
//                         ),
//                       ),
//                     ),
//                     const SizedBox(width: 15),
//                     Expanded(
//                       child: TextField(
//                         controller: _cvcController,
//                         decoration: InputDecoration(
//                           labelText: 'CVC',
//                           hintText: '123',
//                           border: OutlineInputBorder(
//                             borderRadius: BorderRadius.circular(12),
//                           ),
//                         ),
//                         keyboardType: TextInputType.number,
//                       ),
//                     ),
//                   ],
//                 ),
//                 const SizedBox(height: 15),

//                 // Name on card
//                 TextField(
//                   controller: _nameController,
//                   decoration: InputDecoration(
//                     labelText: 'Name on Card',
//                     border: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(12),
//                     ),
//                     prefixIcon: const Icon(Icons.person),
//                   ),
//                 ),
//                 const SizedBox(height: 20),

//                 // Pay button
//                 SizedBox(
//                   width: double.infinity,
//                   height: 50,
//                   child: ElevatedButton(
//                     onPressed: _handlePayment,
//                     style: ElevatedButton.styleFrom(
//                       backgroundColor: AppTheme.accentColor,
//                       foregroundColor: Colors.white,
//                       shape: RoundedRectangleBorder(
//                         borderRadius: BorderRadius.circular(12),
//                       ),
//                       elevation: 2,
//                     ),
//                     child: const Text(
//                       'Pay Now',
//                       style: TextStyle(
//                         fontSize: 16,
//                         fontWeight: FontWeight.bold,
//                         fontFamily: 'Cairo',
//                       ),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           const SizedBox(height: 20),

//           // Secure payment note
//           Row(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               Icon(
//                 Icons.lock,
//                 size: 16,
//                 color: Colors.grey.shade600,
//               ),
//               const SizedBox(width: 8),
//               Text(
//                 'Secure payment via Stripe',
//                 style: TextStyle(
//                   color: Colors.grey.shade600,
//                   fontSize: 14,
//                 ),
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }
// }
