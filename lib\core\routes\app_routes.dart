/// Constants for all application routes
class AppRoutes {
  // Auth routes
  static const String landing = '/landing';
  static const String auth = '/auth';
  static const String register = '/auth/register';
  static const String login = '/auth/login';

  // Main routes
  static const String dashboard = '/dashboard';
  static const String profile = '/profile';
  static const String settings = '/settings';

  // Publisher routes
  static const String listings = '/listings';
  static const String orders = '/orders';
  static const String earnings = '/earnings';
  static const String support = '/support';
  static const String addListing = '/add-listing';
  static const String editListing = '/edit-listing';
  static const String invoices = '/invoices';

  // Buyer routes
  static const String dashboard_buyer = '/dashboard_buyer';
  static const String listings_buyer = '/listings_buyer';
  static const String orders_buyer = '/orders_buyer';
  static const String earnings_buyer = '/earnings_buyer';
  static const String support_buyer = '/support_buyer';
  static const String addListing_buyer = '/add-listing_buyer';
  static const String profile_buyer = '/profile_buyer';
  static const String buy_post_buyer = '/buy_post_buyer';
  static const String invoices_buyer = '/invoices_buyer';

  // Parameterized routes
  static const String listingDetails = '/listings/:id';
  static const String orderDetails = '/orders/:id';
  static const String userProfile = '/profile/:userId';
  static const String editListingWithId = '/edit-listing/:id';

  // Helper methods
  static String getPageName(String route) {
    return route.replaceAll('/', '');
  }

  static bool isPublicRoute(String route) {
    return route.startsWith(auth);
  }

  static bool isPublisherRoute(String route) {
    return route.startsWith('/publisher');
  }

  static bool isProtectedRoute(String route) {
    return !isPublicRoute(route);
  }

  // Parameter handling methods
  static String buildPath(String route, {Map<String, String>? params}) {
    if (params == null) return route;

    String path = route;
    params.forEach((key, value) {
      path = path.replaceAll(':$key', value);
    });
    return path;
  }

  static Map<String, String> extractParams(String route, String path) {
    final Map<String, String> params = {};
    final routeSegments = route.split('/');
    final pathSegments = path.split('/');

    for (int i = 0; i < routeSegments.length; i++) {
      if (routeSegments[i].startsWith(':')) {
        final paramName = routeSegments[i].substring(1);
        if (i < pathSegments.length) {
          params[paramName] = pathSegments[i];
        }
      }
    }
    return params;
  }

  static String buildQueryString(Map<String, dynamic>? queryParams) {
    if (queryParams == null || queryParams.isEmpty) return '';

    final queryString = queryParams.entries
        .map((e) =>
            '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value.toString())}')
        .join('&');

    return queryString.isNotEmpty ? '?$queryString' : '';
  }

  static Map<String, dynamic> parseQueryString(String queryString) {
    if (queryString.isEmpty) return {};

    final uri = Uri.parse('http://dummy.com$queryString');
    return uri.queryParameters;
  }

  // Route building methods
  static String buildListingDetailsPath(String id,
      {Map<String, dynamic>? queryParams}) {
    final path = buildPath(listingDetails, params: {'id': id});
    return path + buildQueryString(queryParams);
  }

  static String buildOrderDetailsPath(String id,
      {Map<String, dynamic>? queryParams}) {
    final path = buildPath(orderDetails, params: {'id': id});
    return path + buildQueryString(queryParams);
  }

  static String buildUserProfilePath(String userId,
      {Map<String, dynamic>? queryParams}) {
    final path = buildPath(userProfile, params: {'userId': userId});
    return path + buildQueryString(queryParams);
  }

  static String buildEditListingPath(String id,
      {Map<String, dynamic>? queryParams}) {
    final path = buildPath(editListingWithId, params: {'id': id});
    return path + buildQueryString(queryParams);
  }
}
