import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts/core/models/website_model.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:guest_posts/core/utils/colors.dart';
import 'package:guest_posts/features/publisher_acc/home/<USER>/bulk_import_dialog.dart';
import 'package:guest_posts/features/publisher_acc/home/<USER>/edit_website.dart';
import 'package:intl/intl.dart';
import 'package:guest_posts/core/theme/app_theme.dart';

class MyWebsitesWidget extends StatefulWidget {
  const MyWebsitesWidget({super.key});

  @override
  State<MyWebsitesWidget> createState() => _MyWebsitesWidgetState();
}

class _MyWebsitesWidgetState extends State<MyWebsitesWidget> {
  final List<String> _tabs = [
    'All',
    'Pending',
    'Approved',
    'On Hold',
    'Rejected',
  ];
  int _selectedTabIndex = 0;
  final TextEditingController _searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SingleChildScrollView(
        child: Container(
          padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              isSmallScreen
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const MyWebSitesHowItWorksWidget(),
                        const SizedBox(height: 24),
                        MyWebsitesContentWidget(
                          tabs: _tabs,
                          selectedTabIndex: _selectedTabIndex,
                          onTabSelected: (index) {
                            if (mounted) {
                              setState(() => _selectedTabIndex = index);
                            }
                          },
                          searchController: _searchController,
                        ),
                      ],
                    )
                  : Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 2,
                          child: MyWebsitesContentWidget(
                            tabs: _tabs,
                            selectedTabIndex: _selectedTabIndex,
                            onTabSelected: (index) {
                              if (mounted) {
                                setState(() => _selectedTabIndex = index);
                              }
                            },
                            searchController: _searchController,
                          ),
                        ),
                        const SizedBox(width: 24),
                        const Expanded(child: MyWebSitesHowItWorksWidget()),
                      ],
                    ),
            ],
          ),
        ),
      ),
    );
  }
}

class MyWebsitesContentWidget extends StatefulWidget {
  final List<String> tabs;
  final int selectedTabIndex;
  final ValueChanged<int> onTabSelected;
  final TextEditingController searchController;

  const MyWebsitesContentWidget({
    super.key,
    required this.tabs,
    required this.selectedTabIndex,
    required this.onTabSelected,
    required this.searchController,
  });

  @override
  State<MyWebsitesContentWidget> createState() =>
      _MyWebsitesContentWidgetState();
}

class _MyWebsitesContentWidgetState extends State<MyWebsitesContentWidget> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  Future<void> _updateWebsiteStatus(
      String docId, String newStatus, bool isActive) async {
    try {
      await _firestore.collection('websites').doc(docId).update({
        'status': newStatus,
        'isActive': isActive,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Website status updated to $newStatus')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Error updating website: $e'),
              backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _deleteWebsite(String docId) async {
    try {
      await _firestore.collection('websites').doc(docId).update({
        'status': 'Deleted',
        'isActive': false,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Website marked as deleted')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Error deleting website: $e'),
              backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<int> _getOrderCount(String websiteId) async {
    final snapshot = await _firestore
        .collection('orders')
        .where('websiteId', isEqualTo: websiteId)
        .where('status', whereIn: ['Pending', 'Approved', 'Completed']).get();
    return snapshot.docs.length;
  }

  Future<void> _showBulkImportDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => const BulkImportDialog(),
    );

    if (result == true && mounted) {
      setState(() {
        // Refresh the list after successful import
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = _auth.currentUser;
    if (user == null) {
      return const Center(child: Text('Please sign in to view your websites'));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Expanded(
                child: Text(
                  'Now you can monetize your site while placing or creating unique and relevant content',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 20,
                    color: Colors.black,
                  ),
                ),
              ),
              Row(
                children: [
                  InkWell(
                    onTap: () => _showBulkImportDialog(context),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: const Color.fromARGB(255, 255, 255, 255),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: const [
                          Icon(Icons.upload_file, size: 18),
                          SizedBox(width: 4),
                          Text(
                            'Bulk Import',
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontSize: 18,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  InkWell(
                    onTap: () => context.go('/add-listing'),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: const Color.fromARGB(255, 255, 255, 255),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'Add Website',
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          fontSize: 18,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: widget.tabs
                .map((tab) => Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: GestureDetector(
                        onTap: () =>
                            widget.onTabSelected(widget.tabs.indexOf(tab)),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: widget.selectedTabIndex ==
                                    widget.tabs.indexOf(tab)
                                ? AppTheme.accentColor
                                : const Color.fromARGB(255, 255, 255, 255),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            tab,
                            style: GoogleFonts.poppins(
                              fontSize: 15,
                              fontWeight: widget.selectedTabIndex ==
                                      widget.tabs.indexOf(tab)
                                  ? FontWeight.w500
                                  : FontWeight.normal,
                              color: widget.selectedTabIndex ==
                                      widget.tabs.indexOf(tab)
                                  ? const Color.fromARGB(255, 255, 255, 255)
                                  : Colors.black,
                            ),
                          ),
                        ),
                      ),
                    ))
                .toList(),
          ),
        ),
        const SizedBox(height: 16),
        TextField(
          controller: widget.searchController,
          decoration: InputDecoration(
            hintText: 'Search by URL or domain',
            hintStyle: GoogleFonts.poppins(fontSize: 16, color: Colors.grey),
            fillColor: Colors.white,
            filled: true,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            prefixIcon: const Icon(Icons.search, color: Colors.grey),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          onChanged: (value) {
            if (mounted) {
              setState(() {});
            }
          },
        ),
        const SizedBox(height: 24),
        StreamBuilder<QuerySnapshot>(
          stream: _firestore
              .collection('websites')
              .where('publisherId', isEqualTo: user.uid)
              .snapshots(),
          builder: (context, snapshot) {
            if (snapshot.hasError) {
              return Center(child: Text('Error: ${snapshot.error}'));
            }
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }
            if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
              return Center(
                child: Column(
                  children: [
                    Icon(Icons.web, size: 64, color: Colors.grey.shade400),
                    const SizedBox(height: 16),
                    const Text('No websites found',
                        style: TextStyle(fontFamily: 'Space', fontSize: 18)),
                  ],
                ),
              );
            }

            final websites = snapshot.data!.docs.map((doc) {
              final data = doc.data() as Map<String, dynamic>;
              return WebsiteModel.fromMap(data);
            }).where((website) {
              final status = website.status.toLowerCase();
              final url = website.url.toLowerCase();
              final domain = website.domainName.toLowerCase();
              final searchText = widget.searchController.text.toLowerCase();
              final selectedTab =
                  widget.tabs[widget.selectedTabIndex].toLowerCase();

              final matchesStatus =
                  selectedTab == 'all' || status == selectedTab;
              final matchesSearch = searchText.isEmpty ||
                  url.contains(searchText) ||
                  domain.contains(searchText);

              return matchesStatus && matchesSearch;
            }).toList();

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Results: ${websites.length}',
                  style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w500,
                      fontSize: 20,
                      color: Colors.black87),
                ),
                const SizedBox(height: 16),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: websites.length,
                  itemBuilder: (context, index) {
                    final website = websites[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  InkWell(
                                    onTap: () async {
                                      final uri =
                                          Uri.parse('https://${website.url}');
                                      if (await canLaunchUrl(uri)) {
                                        await launchUrl(uri);
                                      }
                                    },
                                    child: Text(
                                      website.url,
                                      style: GoogleFonts.poppins(
                                        // fontWeight: FontWeight.bold,
                                        fontSize: 18,
                                        color: AppTheme.accentColor,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 6),
                                    decoration: BoxDecoration(
                                      color: website.status == 'Approved'
                                          ? Colors.green.withOpacity(0.1)
                                          : website.status == 'Pending'
                                              ? Colors.orange.withOpacity(0.1)
                                              : website.status == 'Deleted'
                                                  ? Colors.red.withOpacity(0.1)
                                                  : website.status == 'On Hold'
                                                      ? Colors.grey
                                                          .withOpacity(0.1)
                                                      : website.status ==
                                                              'Rejected'
                                                          ? Colors.red
                                                              .withOpacity(0.1)
                                                          : Colors.blueGrey
                                                              .withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Text(
                                      website.status,
                                      style: GoogleFonts.poppins(
                                        color: website.status == 'Approved'
                                            ? Colors.green
                                            : website.status == 'Pending'
                                                ? Colors.orange
                                                : website.status == 'Deleted'
                                                    ? Colors.red
                                                    : website.status ==
                                                            'On Hold'
                                                        ? Colors.grey
                                                        : website.status ==
                                                                'Rejected'
                                                            ? Colors.red
                                                            : Colors.blueGrey,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  const Spacer(),
                                  FutureBuilder<int>(
                                    future: _getOrderCount(website.websiteId!),
                                    builder: (context, snapshot) {
                                      if (snapshot.connectionState ==
                                          ConnectionState.waiting) {
                                        return const SizedBox.shrink();
                                      }
                                      return Text(
                                        '${snapshot.data ?? 0} Orders',
                                        style: GoogleFonts.poppins(
                                            fontSize: 16,
                                            color: Colors.black54),
                                      );
                                    },
                                  ),
                                  const SizedBox(width: 8),
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: const Icon(FontAwesomeIcons.edit,
                                            color: AppTheme.accentColor),
                                        tooltip: 'Edit',
                                        onPressed: () {
                                          showDialog(
                                            context: context,
                                            builder: (context) => Dialog(
                                              child: ConstrainedBox(
                                                constraints: BoxConstraints(
                                                  maxWidth:
                                                      MediaQuery.of(context)
                                                              .size
                                                              .width *
                                                          0.4,
                                                  maxHeight:
                                                      MediaQuery.of(context)
                                                              .size
                                                              .height *
                                                          0.8,
                                                ),
                                                child: Container(
                                                  padding:
                                                      const EdgeInsets.all(16),
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            16),
                                                  ),
                                                  child: EditWebsitePage(
                                                    websiteId:
                                                        website.websiteId!,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                      PopupMenuButton<String>(
                                        color: Colors.white,
                                        shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                        onSelected: (value) async {
                                          switch (value) {
                                            case 'toggle':
                                              await _updateWebsiteStatus(
                                                website.websiteId!,
                                                website.status == 'Approved'
                                                    ? 'On Hold'
                                                    : 'Approved',
                                                website.status == 'Approved'
                                                    ? false
                                                    : true,
                                              );
                                              break;
                                            case 'delete':
                                              showDialog(
                                                context: context,
                                                builder: (context) =>
                                                    AlertDialog(
                                                  title: const Text(
                                                      'Confirm Delete'),
                                                  content: const Text(
                                                      'Are you sure you want to delete this website?'),
                                                  actions: [
                                                    TextButton(
                                                      onPressed: () =>
                                                          Navigator.pop(
                                                              context),
                                                      child:
                                                          const Text('Cancel'),
                                                    ),
                                                    TextButton(
                                                      onPressed: () {
                                                        _deleteWebsite(
                                                            website.websiteId!);
                                                        Navigator.pop(context);
                                                      },
                                                      child: const Text(
                                                          'Delete',
                                                          style: TextStyle(
                                                              color:
                                                                  Colors.red)),
                                                    ),
                                                  ],
                                                ),
                                              );
                                              break;
                                            case 'view':
                                              context.go(
                                                  '/website/${website.websiteId}');
                                              break;
                                            case 'orders':
                                              context.go(
                                                  '/orders?websiteId=${website.websiteId}');
                                              break;
                                          }
                                        },
                                        itemBuilder: (context) => [
                                          website.status == 'Pending' ||
                                                  website.status ==
                                                      'Rejected' ||
                                                  website.status == 'Deleted'
                                              ? PopupMenuItem(
                                                  value: '',
                                                  child: Center(
                                                    child: Text(website.status,
                                                        style: TextStyle(
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            color: Colors.red)),
                                                  ),
                                                )
                                              : PopupMenuItem(
                                                  value: 'toggle',
                                                  child: Row(
                                                    children: [
                                                      Icon(website.isActive
                                                          ? Icons.pause
                                                          : Icons.play_arrow),
                                                      const SizedBox(width: 8),
                                                      Text(website.isActive
                                                          ? 'Pause'
                                                          : 'Activate'),
                                                    ],
                                                  ),
                                                ),
                                          const PopupMenuItem(
                                            value: 'delete',
                                            child: Row(
                                              children: [
                                                Icon(Icons.delete_outline),
                                                SizedBox(width: 8),
                                                Text('Delete'),
                                              ],
                                            ),
                                          ),
                                          const PopupMenuItem(
                                            value: 'orders',
                                            child: Row(
                                              children: [
                                                Icon(Icons
                                                    .library_books_outlined),
                                                SizedBox(width: 8),
                                                Text('View Orders'),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              Divider(
                                color: AppTheme.lightGrey,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Domain Name: ${website.domainName}',
                                style: const TextStyle(
                                    fontFamily: 'Cairo',
                                    fontSize: 14,
                                    color: Colors.black87),
                              ),
                              Text(
                                'Language: ${website.language}',
                                style: const TextStyle(
                                    fontFamily: 'Cairo',
                                    fontSize: 14,
                                    color: Colors.black87),
                              ),
                              Text(
                                'Categories: ${website.categories.join(', ')}',
                                style: const TextStyle(
                                    fontFamily: 'Cairo',
                                    fontSize: 14,
                                    color: Colors.black87),
                              ),
                              Text(
                                'Base Price: \$${website.basePricing.toStringAsFixed(2)}',
                                style: const TextStyle(
                                    fontFamily: 'Cairo',
                                    fontSize: 14,
                                    color: Colors.black87),
                              ),
                              Text(
                                'Special Topics Additional Price: \$${website.specialTopicsAdditionalPrice.toStringAsFixed(2)}',
                                style: const TextStyle(
                                    fontFamily: 'Cairo',
                                    fontSize: 14,
                                    color: Colors.black87),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Created: ${DateFormat('dd/MM/yyyy').format(website.createdAt.toDate())}',
                                style: const TextStyle(
                                    fontFamily: 'Cairo',
                                    fontSize: 12,
                                    color: Colors.grey),
                              ),
                              if (website.rejectionReason != null) ...[
                                const SizedBox(height: 8),
                                Text(
                                  'Rejection Reason: ${website.rejectionReason}',
                                  style: const TextStyle(
                                      fontFamily: 'Cairo',
                                      fontSize: 12,
                                      color: Colors.red),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ],
            );
          },
        ),
      ],
    );
  }
}

class MyWebSitesHowItWorksWidget extends StatelessWidget {
  const MyWebSitesHowItWorksWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.maxFinite,
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: const Color.fromARGB(255, 255, 255, 255),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        'How it works?',
        style: TextStyle(fontFamily: 'Alatsi', fontSize: 20),
      ),
    );
  }
}
