import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts/core/theme/app_theme.dart';

/// A premium styled metric item for displaying statistics
class PremiumMetricItem extends StatelessWidget {
  final String title;
  final String value;
  final IconData? icon;
  final bool highlight;
  final String? tooltip;
  final double width;
  final double? height;
  final VoidCallback? onTap;
  final String? subtitle;

  const PremiumMetricItem({
    Key? key,
    required this.title,
    required this.value,
    this.icon,
    this.highlight = false,
    this.tooltip,
    this.width = 180,
    this.height,
    this.onTap,
    this.subtitle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final metricWidget = Container(
      width: width,
      height: height,
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
        border: Border.all(color: AppTheme.borderColor),
        boxShadow: [
          BoxShadow(
            color: AppTheme.cardShadow.withOpacity(0.05),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: AppTheme.accentColor,
          ),
          const SizedBox(height: 8),
          Text(title,
              style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87)),
          const Spacer(),
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(value,
                style: GoogleFonts.poppins(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color.fromARGB(255, 31, 21, 48),
                )),
            const SizedBox(height: 4),
            Text(subtitle ?? '',
                style: GoogleFonts.poppins(
                    fontSize: 12, color: Colors.black.withOpacity(0.6))),
            const SizedBox(height: 8),
          ]),
        ],
      ),
    );

    if (tooltip != null) {
      return Tooltip(
        message: tooltip!,
        preferBelow: true,
        textStyle: GoogleFonts.poppins(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppTheme.accentGradientStart.withOpacity(0.9),
              AppTheme.accentGradientEnd.withOpacity(0.9),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: AppTheme.cardShadow.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        child: onTap != null
            ? InkWell(
                onTap: onTap,
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
                child: metricWidget,
              )
            : metricWidget,
      );
    }

    return onTap != null
        ? InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
            child: metricWidget,
          )
        : metricWidget;
  }
}
