import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:guest_posts/core/theme/app_theme.dart'; // Using your AppTheme

// Note: The AppTheme class previously defined here has been removed.
// This widget now relies on the AppTheme provided from 'package:guest_posts/core/theme/app_theme.dart'
// and the ThemeData applied in your MaterialApp.

class PublisherDashboardWidget extends StatefulWidget {
  const PublisherDashboardWidget({super.key});

  @override
  State<PublisherDashboardWidget> createState() =>
      _PublisherDashboardWidgetState();
}

class _PublisherDashboardWidgetState extends State<PublisherDashboardWidget> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  LineChartBarData? _lineChartBarData;
  List<Map<String, dynamic>> _earningsData = [];
  bool _isLoading = true;
  int _completedOrdersAllTime = 0;
  DateTimeRange? _selectedDateRangeForChart;

  @override
  void initState() {
    super.initState();
    _selectedDateRangeForChart = DateTimeRange(
      start: DateTime.now().subtract(const Duration(days: 30)),
      end: DateTime.now(),
    );
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    if (!mounted) return;
    setState(() => _isLoading = true);
    await Future.wait([
      _loadEarningsData(),
      _loadCompletedOrders(),
    ]);
    if (mounted) setState(() => _isLoading = false);
  }

  Future<void> _loadEarningsData() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final historySnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('history')
          .where('type', isEqualTo: 'Order')
          .where('status', isEqualTo: 'Completed')
          .orderBy('date', descending: true)
          .get();

      _earningsData = historySnapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'date': (data['date'] as Timestamp).toDate(),
          'value': (data['amount'] as num).toDouble(),
        };
      }).toList();

      _updateChartData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Error loading earnings: $e',
                  style: const TextStyle(color: Colors.white)),
              backgroundColor:
                  AppTheme.errorColor), // Using your AppTheme.errorColor
        );
      }
      print('Error loading earnings: $e');
    }
  }

  void _updateChartData() {
    if (!mounted) return;
    if (_earningsData.isEmpty || _selectedDateRangeForChart == null) {
      setState(() {
        _lineChartBarData = null;
      });
      return;
    }

    final filteredSpots = _earningsData
        .where((data) =>
            !data['date'].isBefore(_selectedDateRangeForChart!.start) &&
            !data['date'].isAfter(
                _selectedDateRangeForChart!.end.add(const Duration(days: 1))))
        .map((data) {
      double xValue = data['date']
          .difference(_selectedDateRangeForChart!.start)
          .inDays
          .toDouble();
      xValue = xValue < 0 ? 0 : xValue;
      return FlSpot(xValue, data['value'].toDouble());
    }).toList();

    filteredSpots.sort((a, b) => a.x.compareTo(b.x));

    setState(() {
      _lineChartBarData = LineChartBarData(
        spots: filteredSpots,
        isCurved: true,
        color: AppTheme.accentColor, // Using your AppTheme.accentColor
        barWidth: 3,
        isStrokeCapRound: true,
        dotData: FlDotData(
          show: true,
          getDotPainter: (spot, percent, barData, index) => FlDotCirclePainter(
              radius: 4,
              color: AppTheme.accentColor,
              strokeWidth: 1.5,
              strokeColor: AppTheme
                  .componentBackColor), // Assuming componentBackColor for dot stroke
        ),
        belowBarData: BarAreaData(
          show: true,
          gradient: LinearGradient(
            colors: [
              AppTheme.accentColor.withOpacity(0.3),
              AppTheme.accentColor.withOpacity(0.05),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
      );
    });
  }

  Future<void> _loadCompletedOrders() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final ordersSnapshot = await _firestore
          .collection('orders')
          .where('publisherId', isEqualTo: user.uid)
          .where('status', isEqualTo: 'Completed')
          .get();

      if (mounted) {
        setState(() {
          _completedOrdersAllTime = ordersSnapshot.docs.length;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Error loading completed orders: $e',
                  style: const TextStyle(color: Colors.white)),
              backgroundColor:
                  AppTheme.errorColor), // Using your AppTheme.errorColor
        );
      }
      print('Error loading completed orders: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;
    final isMediumScreen = screenWidth >= 600 && screenWidth < 1200;
    final theme = Theme.of(
        context); // This will use the ThemeData provided by your MaterialApp

    if (_auth.currentUser == null) {
      return Scaffold(
        body: Center(
            child: Text('Please sign in to view your dashboard',
                style: theme.textTheme.titleMedium)),
      );
    }

    return StreamBuilder<DocumentSnapshot<Map<String, dynamic>>>(
      stream: _firestore
          .collection('users')
          .doc(_auth.currentUser!.uid)
          .snapshots(),
      builder: (context, userSnapshot) {
        if (userSnapshot.connectionState == ConnectionState.waiting ||
            _isLoading) {
          return Scaffold(
              body: Center(
                  child:
                      CircularProgressIndicator(color: AppTheme.accentColor)));
        }
        if (!userSnapshot.hasData || userSnapshot.data?.data() == null) {
          return Scaffold(
              body: Center(
                  child: Text('User data not found',
                      style: theme.textTheme.titleMedium)));
        }

        final userData = userSnapshot.data!.data()!;
        if (userData['isPublisher'] != true) {
          return Scaffold(
              body: Center(
                  child: Text('This dashboard is for publishers only',
                      style: theme.textTheme.titleMedium)));
        }

        final mainBalance =
            (userData['mainBalance'] as num?)?.toDouble() ?? 0.0;
        final reservedFunds =
            (userData['reservedBalance'] as num?)?.toDouble() ?? 0.0;
        final totalEarnings = mainBalance + reservedFunds;

        return Scaffold(
          backgroundColor:
              AppTheme.backgroundColor, // Using your AppTheme.backgroundColor
          body: SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(bottom: 24.0),
                    child: Text(
                      'Publisher Dashboard',
                      // Assuming your global theme provides Poppins and appropriate text styles
                      style: theme.textTheme.displayMedium?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: AppTheme.textPrimary),
                    ),
                  ),
                  GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount:
                        isSmallScreen ? 1 : (isMediumScreen ? 2 : 4),
                    mainAxisSpacing: isSmallScreen ? 12 : 16,
                    crossAxisSpacing: isSmallScreen ? 12 : 16,
                    childAspectRatio:
                        isSmallScreen ? 2.2 : (isMediumScreen ? 1.8 : 2),
                    children: [
                      _buildMetricCard(
                        context: context,
                        title: 'Available Balance',
                        value: '\$${mainBalance.toStringAsFixed(2)}',
                        icon: FontAwesomeIcons.wallet,
                        iconColor: AppTheme.primaryColor,
                        subtitle: 'Ready to withdraw',
                      ),
                      _buildMetricCard(
                        context: context,
                        title: 'Pending Earnings',
                        value: '\$${reservedFunds.toStringAsFixed(2)}',
                        icon: FontAwesomeIcons.hourglassHalf,
                        iconColor: Colors.orange
                            .shade700, // Consider making this an AppTheme color
                        subtitle: 'Awaiting completion',
                      ),
                      _buildMetricCard(
                        context: context,
                        title: 'Completed Orders',
                        value: _completedOrdersAllTime.toString(),
                        icon: FontAwesomeIcons.solidCheckCircle,
                        iconColor: AppTheme
                            .successColor, // Using your AppTheme.successColor
                        subtitle: 'All time',
                      ),
                      _buildMetricCard(
                        context: context,
                        title: 'Total Revenue',
                        value: '\$${totalEarnings.toStringAsFixed(2)}',
                        icon: FontAwesomeIcons.chartLine,
                        iconColor: AppTheme.accentColor,
                        subtitle: 'All time',
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  LayoutBuilder(
                    builder: (context, constraints) {
                      if (constraints.maxWidth < 800) {
                        return Column(
                          children: [
                            _buildEarningsChartCard(context),
                            const SizedBox(height: 24),
                            OrdersTableWidget(),
                          ],
                        );
                      } else {
                        return Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                                flex: 3,
                                child: _buildEarningsChartCard(context)),
                            const SizedBox(width: 24),
                            Expanded(flex: 2, child: OrdersTableWidget()),
                          ],
                        );
                      }
                    },
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEarningsChartCard(BuildContext context) {
    final theme = Theme.of(context);
    double totalEarningsInPeriod = 0;
    double maxEarningInPeriod = 0;

    if (_lineChartBarData != null && _lineChartBarData!.spots.isNotEmpty) {
      totalEarningsInPeriod =
          _lineChartBarData!.spots.fold(0.0, (sum, spot) => sum + spot.y);
      maxEarningInPeriod = _lineChartBarData!.spots
          .map((spot) => spot.y)
          .reduce((a, b) => a > b ? a : b);
    }

    final int totalDaysInSelectedRange = _selectedDateRangeForChart != null
        ? _selectedDateRangeForChart!.end
                .difference(_selectedDateRangeForChart!.start)
                .inDays +
            1
        : 30;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme
            .componentBackColor, // Using your AppTheme.componentBackColor for card background
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.cardShadow ??
                Colors.grey.withOpacity(
                    0.08), // Using your AppTheme.cardShadow or a default
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Earnings Overview',
                style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600, color: AppTheme.textPrimary),
              ),
              TextButton.icon(
                onPressed: _selectDateRangeForChart,
                icon: const Icon(FontAwesomeIcons.calendarDays,
                    size: 18, color: AppTheme.accentColor),
                label: Text(
                  _selectedDateRangeForChart != null
                      ? '${_formatChartDate(_selectedDateRangeForChart!.start)} - ${_formatChartDate(_selectedDateRangeForChart!.end)}'
                      : 'Select Range',
                  style: theme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.accentColor, fontWeight: FontWeight.w500),
                ),
                style: TextButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8)),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          SizedBox(
            height: 300,
            child: _isLoading ||
                    _lineChartBarData == null ||
                    _lineChartBarData!.spots.isEmpty
                ? Center(
                    child: _isLoading
                        ? const CircularProgressIndicator(
                            color: AppTheme.accentColor)
                        : Text('No earnings data for selected period.',
                            style: theme.textTheme.bodyMedium
                                ?.copyWith(color: AppTheme.textSecondary)),
                  )
                : LineChart(
                    LineChartData(
                      gridData: FlGridData(
                        show: true,
                        drawVerticalLine: true,
                        verticalInterval: totalDaysInSelectedRange > 30
                            ? (totalDaysInSelectedRange / 5).roundToDouble()
                            : 7,
                        getDrawingHorizontalLine: (value) {
                          return FlLine(
                              color: AppTheme.borderColor ??
                                  Colors.grey.withOpacity(0.2),
                              strokeWidth: 1); // Using AppTheme.borderColor
                        },
                        getDrawingVerticalLine: (value) {
                          return FlLine(
                              color: AppTheme.borderColor ??
                                  Colors.grey.withOpacity(0.2),
                              strokeWidth: 1); // Using AppTheme.borderColor
                        },
                      ),
                      titlesData: FlTitlesData(
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            reservedSize: 30,
                            interval: totalDaysInSelectedRange > 30
                                ? (totalDaysInSelectedRange / 5).roundToDouble()
                                : 7,
                            getTitlesWidget: (value, meta) {
                              if (_selectedDateRangeForChart == null)
                                return const SizedBox.shrink();
                              final date = _selectedDateRangeForChart!.start
                                  .add(Duration(days: value.toInt()));
                              if (totalDaysInSelectedRange > 60 &&
                                  value.toInt() %
                                          ((totalDaysInSelectedRange / 30)
                                                  .round() *
                                              2) !=
                                      0) {
                                return const SizedBox.shrink();
                              } else if (totalDaysInSelectedRange > 30 &&
                                  value.toInt() % 2 != 0 &&
                                  totalDaysInSelectedRange <= 60) {
                                return const SizedBox.shrink();
                              }
                              return Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: Text(
                                  '${date.day}/${date.month}',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                      fontSize: 10,
                                      color: AppTheme.textSecondary),
                                ),
                              );
                            },
                          ),
                        ),
                        leftTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            reservedSize: 45,
                            getTitlesWidget: (value, meta) {
                              if (value == meta.max || value == meta.min)
                                return const SizedBox.shrink();
                              return Padding(
                                padding:
                                    const EdgeInsets.only(left: 0, right: 6),
                                child: Text(
                                  '\$${value.toInt()}',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                      fontSize: 10,
                                      color: AppTheme.textSecondary),
                                ),
                              );
                            },
                            interval: (_lineChartBarData != null &&
                                    _lineChartBarData!.spots.isNotEmpty)
                                ? (maxEarningInPeriod / 4)
                                    .clamp(1.0, double.infinity)
                                : 25,
                          ),
                        ),
                        rightTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false)),
                        topTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false)),
                      ),
                      borderData: FlBorderData(
                        show: true,
                        border: Border(
                          bottom: BorderSide(
                              color: AppTheme.borderColor ??
                                  Colors.grey.withOpacity(0.3),
                              width: 1), // Using AppTheme.borderColor
                          left: BorderSide(
                              color: AppTheme.borderColor ??
                                  Colors.grey.withOpacity(0.3),
                              width: 1), // Using AppTheme.borderColor
                        ),
                      ),
                      minX: 0,
                      maxX: totalDaysInSelectedRange > 1
                          ? totalDaysInSelectedRange.toDouble() - 1
                          : 1,
                      minY: 0,
                      maxY: (_lineChartBarData != null &&
                              _lineChartBarData!.spots.isNotEmpty)
                          ? (maxEarningInPeriod * 1.2)
                              .clamp(10.0, double.infinity)
                          : 50,
                      lineBarsData: [_lineChartBarData!],
                      lineTouchData: LineTouchData(
                        touchTooltipData: LineTouchTooltipData(
                          // tooltipBgColor:
                          //     AppTheme.primaryColor.withOpacity(0.85),
                          getTooltipItems: (touchedSpots) {
                            return touchedSpots.map((LineBarSpot touchedSpot) {
                              final textStyle = TextStyle(
                                // Assuming Poppins is globally set
                                color: AppTheme
                                    .componentBackColor, // Text on tooltip
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              );
                              return LineTooltipItem(
                                  '\$${touchedSpot.y.toStringAsFixed(2)}',
                                  textStyle);
                            }).toList();
                          },
                        ),
                        handleBuiltInTouches: true,
                      ),
                    ),
                  ),
          ),
          const SizedBox(height: 20),
          if (_lineChartBarData != null && _lineChartBarData!.spots.isNotEmpty)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildChartSummaryItem(
                    context,
                    'Total',
                    '\$${totalEarningsInPeriod.toStringAsFixed(2)}',
                    AppTheme.successColor),
                _buildChartSummaryItem(
                    context,
                    'Max Daily',
                    '\$${maxEarningInPeriod.toStringAsFixed(2)}',
                    AppTheme.accentColor),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildChartSummaryItem(
      BuildContext context, String title, String value, Color valueColor) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title,
            style: theme.textTheme.bodyMedium
                ?.copyWith(color: AppTheme.textSecondary)),
        const SizedBox(height: 4),
        Text(value,
            style: theme.textTheme.titleMedium
                ?.copyWith(fontWeight: FontWeight.w600, color: valueColor)),
      ],
    );
  }

  String _formatChartDate(DateTime date) {
    return '${_getMonthAbbreviation(date.month)} ${date.day}, ${date.year}';
  }

  String _getMonthAbbreviation(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[month - 1];
  }

  Future<void> _selectDateRangeForChart() async {
    DateTimeRange? picked = await showDateRangePicker(
      context: context,
      initialDateRange: _selectedDateRangeForChart,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 1)),
      builder: (context, child) {
        // Assuming your global theme for DatePicker is set or you customize it here
        return Theme(
          data: Theme.of(context).copyWith(
            // Further customize if needed, or rely on global theme
            colorScheme:
                (Theme.of(context).colorScheme ?? const ColorScheme.light())
                    .copyWith(
              primary: AppTheme.primaryColor,
              onPrimary: AppTheme.componentBackColor, // Text on primary color
              surface: AppTheme.componentBackColor,
              onSurface: AppTheme.textPrimary,
            ),
            dialogBackgroundColor: AppTheme.componentBackColor,
          ),
          child: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
              child: child,
            ),
          ),
        );
      },
    );
    if (picked != null && picked != _selectedDateRangeForChart) {
      if (mounted) {
        setState(() {
          _selectedDateRangeForChart = picked;
        });
        _updateChartData();
      }
    }
  }

  Widget _buildMetricCard({
    required BuildContext context,
    required String title,
    required String value,
    required IconData icon,
    required Color iconColor,
    String? trend,
    bool? isPositive,
    required String subtitle,
  }) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme
            .componentBackColor, // Using your AppTheme.componentBackColor
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.cardShadow ??
                Colors.grey.withOpacity(0.08), // Using your AppTheme.cardShadow
            blurRadius: 15,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(title,
                  style: theme.textTheme.titleMedium?.copyWith(
                      color: AppTheme.textSecondary,
                      fontWeight: FontWeight.w500)),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: iconColor.withOpacity(0.15),
                  shape: BoxShape.circle,
                ),
                child: FaIcon(icon, size: 18, color: iconColor),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(value,
              style: theme.textTheme.displaySmall?.copyWith(
                  fontWeight: FontWeight.w700, color: AppTheme.textPrimary)),
          const SizedBox(height: 4),
          Row(
            children: [
              if (trend != null && isPositive != null)
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      isPositive
                          ? Icons.arrow_upward_rounded
                          : Icons.arrow_downward_rounded,
                      color: isPositive
                          ? AppTheme.successColor
                          : AppTheme.errorColor,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      trend,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: isPositive
                            ? AppTheme.successColor
                            : AppTheme.errorColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 6),
                  ],
                ),
              Expanded(
                child: Text(
                  subtitle,
                  style: theme.textTheme.bodySmall
                      ?.copyWith(color: AppTheme.textSecondary),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class OrdersTableWidget extends StatefulWidget {
  const OrdersTableWidget({super.key});

  @override
  State<OrdersTableWidget> createState() => _OrdersTableWidgetState();
}

class _OrdersTableWidgetState extends State<OrdersTableWidget> {
  String _selectedTab = 'All';
  final TextEditingController _searchController = TextEditingController();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  List<Map<String, dynamic>> _orders = [];
  bool _isLoading = true;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadOrders();
    _searchController.addListener(() {
      if (mounted) setState(() {});
    });
  }

  Future<void> _loadOrders() async {
    if (!mounted) return;
    setState(() => _isLoading = true);
    try {
      final user = _auth.currentUser;
      if (user == null) {
        if (mounted) setState(() => _isLoading = false);
        return;
      }

      final ordersSnapshot = await _firestore
          .collection('orders')
          .where('publisherId', isEqualTo: user.uid)
          .orderBy('orderDate', descending: true)
          .limit(50)
          .get();

      _orders = ordersSnapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'orderId': doc.id,
          'website': data['websiteUrl'] ?? 'N/A',
          'amount': (data['totalPrice'] as num?)?.toDouble() ?? 0.0,
          'status':
              (data['status'] ?? 'Unknown').toString().capitalizeFirstLetter(),
          'createdAt': data['orderDate'] as Timestamp?,
        };
      }).toList();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Error loading orders: $e',
                  style: const TextStyle(
                      color:
                          Colors.white)), // Assuming white text on error color
              backgroundColor: AppTheme.errorColor),
        );
      }
      print('Error loading orders: $e');
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  List<Map<String, dynamic>> get _filteredAndSearchedOrders {
    List<Map<String, dynamic>> tempOrders = [];
    if (_selectedTab == 'All') {
      tempOrders = _orders;
    } else {
      tempOrders =
          _orders.where((order) => order['status'] == _selectedTab).toList();
    }

    if (_searchController.text.isNotEmpty) {
      final searchTerm = _searchController.text.toLowerCase();
      tempOrders = tempOrders
          .where((order) =>
              order['orderId'].toString().toLowerCase().contains(searchTerm) ||
              order['website'].toString().toLowerCase().contains(searchTerm))
          .toList();
    }
    return tempOrders;
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Widget _buildTab(BuildContext context, String title) {
    final theme = Theme.of(context);
    final bool isSelected = _selectedTab == title;
    return GestureDetector(
      onTap: () {
        if (mounted) setState(() => _selectedTab = title);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color:
              isSelected ? AppTheme.primaryColor : AppTheme.componentBackColor,
          borderRadius: BorderRadius.circular(10),
          border: isSelected
              ? null
              : Border.all(
                  color: AppTheme.borderColor ?? Colors.grey.shade300,
                  width: 1),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                      color: AppTheme.primaryColor.withOpacity(0.3),
                      blurRadius: 6,
                      offset: const Offset(0, 3))
                ]
              : [],
        ),
        child: Text(
          title,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isSelected
                ? (AppTheme.componentBackColor ?? Colors.white)
                : AppTheme.textPrimary, // Ensure contrast for selected tab text
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
      ),
    );
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return '${date.day} ${_getMonthAbbreviation(date.month)}, ${date.year}';
  }

  String _getMonthAbbreviation(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[month - 1];
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final displayedOrders = _filteredAndSearchedOrders;

    return Container(
      constraints: const BoxConstraints(minHeight: 400, maxHeight: 600),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme
            .componentBackColor, // Using your AppTheme.componentBackColor
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
              color: AppTheme.cardShadow ??
                  Colors.grey
                      .withOpacity(0.08), // Using your AppTheme.cardShadow
              blurRadius: 20,
              offset: const Offset(0, 5))
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Recent Orders',
                  style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimary)),
              SizedBox(
                width: MediaQuery.of(context).size.width > 600 ? 250 : 180,
                child: TextField(
                  controller: _searchController,
                  style: TextStyle(
                      color: AppTheme
                          .textPrimary), // Ensure input text color matches theme
                  decoration: InputDecoration(
                    hintText: 'Search by ID or Website',
                    hintStyle: TextStyle(
                        color: AppTheme.textSecondary.withOpacity(0.8)),
                    prefixIcon: Icon(FontAwesomeIcons.magnifyingGlass,
                        size: 16, color: AppTheme.textSecondary),
                    // Assuming your global InputDecorationTheme is set via AppTheme.
                    // If not, you might need to style border, fillColor etc. here or in your AppTheme's ThemeData
                    fillColor:
                        AppTheme.lightGrey, // from user's AppColors -> AppTheme
                    filled: true,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide:
                          BorderSide(color: AppTheme.accentColor, width: 1.5),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 16),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                _buildTab(context, 'All'),
                const SizedBox(width: 10),
                _buildTab(context, 'Pending'),
                const SizedBox(width: 10),
                _buildTab(context, 'Approved'),
                const SizedBox(width: 10),
                _buildTab(context, 'In Progress'),
                const SizedBox(width: 10),
                _buildTab(context, 'Completed'),
                const SizedBox(width: 10),
                _buildTab(context, 'Cancelled'),
              ],
            ),
          ),
          const SizedBox(height: 16),
          if (_isLoading)
            const Expanded(
                child: Center(
                    child:
                        CircularProgressIndicator(color: AppTheme.accentColor)))
          else if (displayedOrders.isEmpty)
            Expanded(
              child: Center(
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      FaIcon(FontAwesomeIcons.fileCircleXmark,
                          size: 48,
                          color: AppTheme.textSecondary.withOpacity(0.5)),
                      const SizedBox(height: 16),
                      Text('No orders found.',
                          style: theme.textTheme.titleMedium
                              ?.copyWith(color: AppTheme.textSecondary)),
                      if (_searchController.text.isNotEmpty)
                        Text('Try adjusting your search or filter.',
                            style: theme.textTheme.bodyMedium
                                ?.copyWith(color: AppTheme.textSecondary)),
                    ]),
              ),
            )
          else
            Expanded(
              child: ListView.separated(
                controller: _scrollController,
                itemCount: displayedOrders.length,
                shrinkWrap: true,
                physics: const BouncingScrollPhysics(),
                separatorBuilder: (context, index) => Divider(
                    height: 1,
                    thickness: 1,
                    color: (AppTheme.borderColor ?? Colors.grey.shade200)
                        .withOpacity(0.5),
                    indent: 16,
                    endIndent: 16),
                itemBuilder: (context, index) {
                  final order = displayedOrders[index];
                  return OrderPremiumCard(
                    orderId: order['orderId'],
                    website: order['website'],
                    amount: order['amount'],
                    status: order['status'],
                    date: _formatDate(order['createdAt']),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }
}

class OrderPremiumCard extends StatelessWidget {
  final String orderId;
  final String website;
  final double amount;
  final String status;
  final String date;

  const OrderPremiumCard({
    super.key,
    required this.orderId,
    required this.website,
    required this.amount,
    required this.status,
    required this.date,
  });

  Color _statusColor(String status, BuildContext context) {
    switch (status.toLowerCase()) {
      case 'completed':
        return AppTheme.successColor;
      case 'pending':
        return AppTheme
            .warningColor; // Using your AppTheme.warningColor for pending
      case 'approved':
        return AppTheme
            .infoColor; // Using your AppTheme.infoColor for approved (or accentColor)
      case 'in progress':
        return Colors.purple[600]!; // Match the color used in the orders page
      case 'cancelled':
      case 'declined':
        return AppTheme.errorColor;
      default:
        return AppTheme.textSecondary;
    }
  }

  IconData _statusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return FontAwesomeIcons.solidCircleCheck;
      case 'pending':
        return FontAwesomeIcons.solidClock;
      case 'approved':
        return FontAwesomeIcons.solidThumbsUp;
      case 'in progress':
        return FontAwesomeIcons.spinner;
      case 'cancelled':
      case 'declined':
        return FontAwesomeIcons.solidCircleXmark;
      default:
        return FontAwesomeIcons.solidQuestionCircle;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final statusColor = _statusColor(status, context);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          // TODO: Implement order detail navigation or action
          print("Tapped on order: $orderId");
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.12),
                  shape: BoxShape.circle,
                ),
                child:
                    FaIcon(_statusIcon(status), color: statusColor, size: 20),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      website,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      "ID: $orderId",
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondary,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '\$${amount.toStringAsFixed(2)}',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    date,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

extension StringCasingExtension on String {
  String capitalizeFirstLetter() =>
      isNotEmpty ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : '';
}
