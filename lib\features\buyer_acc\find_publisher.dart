import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:country_picker/country_picker.dart';
import 'package:expandable/expandable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:guest_posts/core/routes/app_routes.dart';
import 'package:guest_posts/core/theme/app_theme.dart';
import 'package:guest_posts/core/utils/app_icons.dart';
import 'package:guest_posts/core/widgets/premium_widgets.dart';
import 'package:guest_posts/features/buyer_acc/buy_post_page.dart';

class HowItWorksWidget extends StatelessWidget {
  const HowItWorksWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.greenAccent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        'How it works?',
        style: TextStyle(fontFamily: 'Alatsi', fontSize: 20),
      ),
    );
  }
}

class FindPublisherPage extends StatefulWidget {
  const FindPublisherPage({super.key});

  @override
  State<FindPublisherPage> createState() => _FindPublisherPageState();
}

class _FindPublisherPageState extends State<FindPublisherPage> {
  final TextEditingController _searchController = TextEditingController();
  late ExpandableController _expandableController;
  final ScrollController _scrollController = ScrollController();

  String _searchQuery = '';
  String? _selectedCategory;
  double? _minDA;
  double? _maxDA;
  double? _minDR;
  double? _maxDR;
  double? _minTraffic;
  double? _maxTraffic;
  double? _minPrice;
  double? _maxPrice;
  double? _minSpamScore;
  double? _maxSpamScore;
  String? _selectedLanguage;
  String? _selectedCountry;

  final CollectionReference _websitesCollection =
      FirebaseFirestore.instance.collection('websites');
  List<String> _categories = [];
  List<String> _languages = [];
  List<QueryDocumentSnapshot> _allWebsites = [];
  bool _isLoadingMore = false;
  bool _hasMoreData = true;
  DocumentSnapshot? _lastDocument;
  final int _pageSize = 20;

  @override
  void initState() {
    super.initState();
    _expandableController = ExpandableController();
    _scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchCategoriesAndLanguages();
      _fetchInitialWebsites();
    });
  }

  @override
  void didUpdateWidget(covariant FindPublisherPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    _scrollController.jumpTo(0);
    _fetchInitialWebsites();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _expandableController.dispose();
    super.dispose();
  }

  Future<void> _fetchCategoriesAndLanguages() async {
    try {
      final snapshot = await _websitesCollection
          .where('status', isEqualTo: 'Approved') // Fetch active websites
          .where('isActive', isEqualTo: true)
          .get();

      final Set<String> categoriesSet = {'All'};
      final Set<String> languagesSet = {};

      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final categories = (data['categories'] as List?)?.cast<String>() ?? [];
        categoriesSet.addAll(categories);
        final language = data['language']?.toString();
        if (language != null && language.isNotEmpty) {
          languagesSet.add(language);
        }
      }

      if (mounted) {
        setState(() {
          _categories = categoriesSet.toList()..sort();
          _languages = languagesSet.toList()..sort();
          _selectedCategory = 'All';
        });
      }
    } catch (e) {
      // Error handled silently
    }
  }

  Future<void> _fetchInitialWebsites() async {
    setState(() {
      _allWebsites.clear();
      _lastDocument = null;
      _hasMoreData = true;
    });

    await _fetchMoreWebsites();
  }

  Future<void> _fetchMoreWebsites() async {
    if (_isLoadingMore || !_hasMoreData) return;

    if (mounted) {
      setState(() => _isLoadingMore = true);
    }

    try {
      Query query = _buildFirestoreQuery();
      if (_lastDocument != null) {
        query = query.startAfterDocument(_lastDocument!);
      }

      final snapshot = await query.get();
      final newWebsites = snapshot.docs;

      setState(() {
        _allWebsites.addAll(newWebsites);
        _lastDocument = newWebsites.isNotEmpty ? newWebsites.last : null;
        _hasMoreData = newWebsites.length == _pageSize;
        _isLoadingMore = false;
      });
    } catch (e) {
      if (mounted) {
        print('Error fetching websites: $e');
        setState(() => _isLoadingMore = false);
      }
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _fetchMoreWebsites();
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      return const Scaffold(
        body: Center(child: Text('Please sign in to view publishers')),
      );
    }

    return Scaffold(
      body: LayoutBuilder(
        builder: (context, constraints) {
          final safeConstraints = BoxConstraints(
            minWidth: constraints.maxWidth.clamp(0, double.infinity),
            minHeight: constraints.maxHeight.clamp(0, double.infinity),
            maxWidth: constraints.maxWidth,
            maxHeight: constraints.maxHeight,
          );

          return Container(
            color: AppTheme.backgroundColor,
            constraints: safeConstraints,
            padding: EdgeInsets.symmetric(
              horizontal: safeConstraints.maxWidth < 600 ? 16.0 : 24.0,
              vertical: 24.0,
            ),
            child: _buildMainContent(safeConstraints),
          );
        },
      ),
    );
  }

  Widget _buildMainContent(BoxConstraints constraints) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      // appBar: AppBar(
      //   backgroundColor: AppTheme.backgroundColor,
      //   elevation: 0,
      //   title: Row(
      //     children: [
      //       Container(
      //         padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      //         decoration: BoxDecoration(
      //           gradient: AppTheme.primaryGradient,
      //           borderRadius: BorderRadius.circular(8),
      //         ),
      //         child: Text(
      //           'PREMIUM',
      //           style: GoogleFonts.poppins(
      //             fontSize: 12,
      //             fontWeight: FontWeight.w600,
      //             color: Colors.white,
      //           ),
      //         ),
      //       ),
      //       const SizedBox(width: 12),
      //       Text(
      //         'Search for verified sites',
      //         style: GoogleFonts.poppins(
      //           fontSize: 24,
      //           fontWeight: FontWeight.bold,
      //           color: AppTheme.textPrimary,
      //         ),
      //       ),
      //     ],
      //   ),
      // ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search bar section
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: SizedBox(
              width: constraints.maxWidth < 600 ? double.infinity : 800,
              child: _buildSearchBar(),
            ),
          ),

          // Filter section
          ExpandableNotifier(
            controller: _expandableController,
            child: Expandable(
              collapsed: const SizedBox.shrink(),
              expanded: _buildFilterSection(),
            ),
          ),

          // Category tabs with result count
          if (_categories.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 1.0),
              child: _buildCategoryTabs(),
            ),

          // Website list - using Expanded to fill available space
          Expanded(
            child: _allWebsites.isNotEmpty
                ? _buildWebsiteList()
                : Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 40,
                          height: 40,
                          child: CircularProgressIndicator(
                            color: AppTheme.accentColor,
                            strokeWidth: 3,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Loading sites...',
                          style: GoogleFonts.poppins(
                            color: AppTheme.textSecondary,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppTheme.cardShadow.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 5,
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        style: GoogleFonts.poppins(
          fontSize: 15,
          color: AppTheme.textPrimary,
        ),
        decoration: InputDecoration(
          filled: true,
          fillColor: Colors.white,
          hintText: 'Search domains',
          hintStyle: GoogleFonts.poppins(
            color: AppTheme.textSecondary.withOpacity(0.7),
            fontSize: 15,
          ),
          border: OutlineInputBorder(
            borderSide: BorderSide(color: AppTheme.borderColor, width: 1),
            borderRadius: BorderRadius.circular(12),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: AppTheme.borderColor, width: 1),
            borderRadius: BorderRadius.circular(12),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: AppTheme.accentColor, width: 1.5),
            borderRadius: BorderRadius.circular(12),
          ),
          prefixIcon: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Icon(Icons.search, color: AppTheme.textSecondary),
          ),
          suffixIcon: Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.accentColor.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: InkWell(
              onTap: () {
                if (mounted) {
                  setState(() => _expandableController.toggle());
                }
              },
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Filters',
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      _expandableController.expanded
                          ? Icons.expand_less
                          : Icons.expand_more,
                      color: Colors.white,
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
          contentPadding:
              const EdgeInsets.symmetric(vertical: 18, horizontal: 16),
        ),
        onSubmitted: (value) {
          if (mounted) {
            setState(() {
              _searchQuery = value.trim();
              _fetchInitialWebsites();
            });
          }
        },
      ),
    );
  }

  Widget _buildCategoryTabs() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: AppTheme.borderColor, width: 1),
            ),
          ),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: _categories.map((category) {
                final isSelected = _selectedCategory == category;
                return Padding(
                  padding: const EdgeInsets.only(right: 36.0),
                  child: InkWell(
                    hoverColor: Colors.transparent,
                    onTap: () {
                      if (mounted) {
                        setState(() {
                          _selectedCategory = category;
                          _fetchInitialWebsites();
                        });
                      }
                    },
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16.0),
                          child: Row(
                            children: [
                              Text(
                                category,
                                style: GoogleFonts.poppins(
                                  color: isSelected
                                      ? AppTheme.accentColor
                                      : AppTheme.textPrimary,
                                  fontWeight: isSelected
                                      ? FontWeight.w600
                                      : FontWeight.w500,
                                  fontSize: 15,
                                ),
                              ),
                              if (isSelected && category != 'All') ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color:
                                        AppTheme.accentColor.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    '${_allWebsites.length}',
                                    style: GoogleFonts.poppins(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      color: AppTheme.accentColor,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          height: 3,
                          width: 100,
                          decoration: BoxDecoration(
                            gradient:
                                isSelected ? AppTheme.primaryGradient : null,
                            color: isSelected ? null : Colors.transparent,
                            borderRadius: BorderRadius.circular(1.5),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppTheme.lightGrey,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppTheme.borderColor),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.format_list_numbered_rounded,
                      size: 16,
                      color: AppTheme.textSecondary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Results: ${_allWebsites.length} websites',
                      style: GoogleFonts.poppins(
                        color: AppTheme.textPrimary,
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              // Container(
              //   padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
              //   decoration: BoxDecoration(
              //     color: Colors.white,
              //     borderRadius: BorderRadius.circular(8),
              //     border: Border.all(color: AppTheme.borderColor),
              //   ),
              //   child: DropdownButton<String>(
              //     value: 'From the lowest price',
              //     icon: Icon(
              //       Icons.keyboard_arrow_down_rounded,
              //       color: AppTheme.accentColor,
              //     ),
              //     underline: Container(height: 0),
              //     style: GoogleFonts.poppins(
              //       color: AppTheme.textPrimary,
              //       fontSize: 14,
              //       fontWeight: FontWeight.w500,
              //     ),
              //     padding: const EdgeInsets.symmetric(horizontal: 12),
              //     borderRadius: BorderRadius.circular(8),
              //     onChanged: (String? newValue) {
              //       // Sorting logic would go here
              //     },
              //     items: <String>[
              //       'From the lowest price',
              //       'From the highest price',
              //       'Newest first',
              //       'Oldest first',
              //     ].map<DropdownMenuItem<String>>((String value) {
              //       return DropdownMenuItem<String>(
              //         value: value,
              //         child: Text(value),
              //       );
              //     }).toList(),
              //   ),
              // ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFilterSection() {
    return Container(
      margin: const EdgeInsets.only(top: 16.0, bottom: 24.0),
      padding: const EdgeInsets.all(28.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppTheme.borderColor),
        boxShadow: [
          BoxShadow(
            color: AppTheme.cardShadow.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 10),
            spreadRadius: 5,
          ),
        ],
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.white,
            AppTheme.componentBackColor.withOpacity(0.3),
          ],
          stops: const [0.0, 0.7, 1.0],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(bottom: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        gradient: AppTheme.primaryGradient,
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.accentColor.withOpacity(0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.filter_list_rounded,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 14),
                    Text(
                      'Advanced Filters',
                      style: GoogleFonts.poppins(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                  ],
                ),
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _minDA = null;
                      _maxDA = null;
                      _minDR = null;
                      _maxDR = null;
                      _minTraffic = null;
                      _maxTraffic = null;
                      _minPrice = null;
                      _maxPrice = null;
                      _minSpamScore = null;
                      _maxSpamScore = null;
                      _selectedLanguage = null;
                      _selectedCountry = null;
                      _fetchInitialWebsites();
                    });
                  },
                  icon: Icon(Icons.refresh_rounded,
                      size: 18, color: AppTheme.accentColor),
                  label: Text(
                    'Reset all',
                    style: GoogleFonts.poppins(
                      color: AppTheme.accentColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    backgroundColor: AppTheme.accentColor.withOpacity(0.05),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          LayoutBuilder(
            builder: (context, constraints) {
              return Wrap(
                spacing: 24.0,
                runSpacing: 24.0,
                children: [
                  _buildFilterGroup(
                    'Price Range',
                    [
                      SizedBox(
                        width:
                            _getFieldWidth(constraints.maxWidth, factor: 0.75),
                        child: _buildNumberField('Min', (value) {
                          _minPrice = double.tryParse(value);
                          if (mounted) {
                            _fetchInitialWebsites();
                          }
                        }),
                      ),
                      const SizedBox(width: 12),
                      SizedBox(
                        width:
                            _getFieldWidth(constraints.maxWidth, factor: 0.75),
                        child: _buildNumberField('Max', (value) {
                          _maxPrice = double.tryParse(value);
                          if (mounted) {
                            _fetchInitialWebsites();
                          }
                        }),
                      ),
                    ],
                  ),
                  _buildFilterGroup(
                    'Ahrefs Domain Rating',
                    [
                      SizedBox(
                        width:
                            _getFieldWidth(constraints.maxWidth, factor: 0.75),
                        child: _buildNumberField('Min', (value) {
                          _minDR = double.tryParse(value);
                          if (mounted) {
                            _fetchInitialWebsites();
                          }
                        }),
                      ),
                      const SizedBox(width: 12),
                      SizedBox(
                        width:
                            _getFieldWidth(constraints.maxWidth, factor: 0.75),
                        child: _buildNumberField('Max', (value) {
                          _maxDR = double.tryParse(value);
                          if (mounted) {
                            _fetchInitialWebsites();
                          }
                        }),
                      ),
                    ],
                  ),
                  _buildFilterGroup(
                    'Moz Domain Authority',
                    [
                      SizedBox(
                        width:
                            _getFieldWidth(constraints.maxWidth, factor: 0.75),
                        child: _buildNumberField('Min', (value) {
                          _minDA = double.tryParse(value);
                          if (mounted) {
                            _fetchInitialWebsites();
                          }
                        }),
                      ),
                      const SizedBox(width: 12),
                      SizedBox(
                        width:
                            _getFieldWidth(constraints.maxWidth, factor: 0.75),
                        child: _buildNumberField('Max', (value) {
                          _maxDA = double.tryParse(value);
                          if (mounted) {
                            _fetchInitialWebsites();
                          }
                        }),
                      ),
                    ],
                  ),
                  _buildFilterGroup(
                    'Organic Traffic',
                    [
                      SizedBox(
                        width:
                            _getFieldWidth(constraints.maxWidth, factor: 0.75),
                        child: _buildNumberField('Min', (value) {
                          _minTraffic = double.tryParse(value);
                          if (mounted) {
                            _fetchInitialWebsites();
                          }
                        }),
                      ),
                      const SizedBox(width: 12),
                      SizedBox(
                        width:
                            _getFieldWidth(constraints.maxWidth, factor: 0.75),
                        child: _buildNumberField('Max', (value) {
                          _maxTraffic = double.tryParse(value);
                          if (mounted) {
                            _fetchInitialWebsites();
                          }
                        }),
                      ),
                    ],
                  ),
                  _buildFilterGroup(
                    'Language',
                    [
                      SizedBox(
                        width: _getFieldWidth(constraints.maxWidth,
                            isDropdown: true),
                        child: DropdownMenu<String>(
                          initialSelection: _selectedLanguage,
                          hintText: 'Select language',
                          inputDecorationTheme: InputDecorationTheme(
                            fillColor: Colors.white,
                            filled: true,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide:
                                  BorderSide(color: AppTheme.borderColor),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide:
                                  BorderSide(color: AppTheme.borderColor),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                  color: AppTheme.accentColor, width: 1.5),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 16),
                          ),
                          textStyle: GoogleFonts.poppins(
                            fontSize: 14,
                            color: AppTheme.textPrimary,
                          ),
                          leadingIcon: Icon(
                            Icons.language_rounded,
                            color: AppTheme.textSecondary,
                            size: 20,
                          ),
                          dropdownMenuEntries: _languages.map((language) {
                            return DropdownMenuEntry<String>(
                              value: language,
                              label: language,
                              style: MenuItemButton.styleFrom(
                                backgroundColor: Colors.white,
                                textStyle: GoogleFonts.poppins(
                                  fontSize: 14,
                                  color: AppTheme.textPrimary,
                                ),
                              ),
                            );
                          }).toList(),
                          onSelected: (value) {
                            if (mounted) {
                              setState(() {
                                _selectedLanguage = value;
                                _fetchInitialWebsites();
                              });
                            }
                          },
                          menuStyle: MenuStyle(
                            backgroundColor:
                                MaterialStateProperty.all(Colors.white),
                            elevation: MaterialStateProperty.all(8),
                            shadowColor:
                                MaterialStateProperty.all(AppTheme.cardShadow),
                            padding: MaterialStateProperty.all(
                              const EdgeInsets.symmetric(vertical: 8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  _buildFilterGroup(
                    'Country',
                    [
                      SizedBox(
                        width: _getFieldWidth(constraints.maxWidth,
                            isDropdown: true),
                        child: TextFormField(
                          readOnly: true,
                          decoration: InputDecoration(
                            hintText: _selectedCountry ?? 'Select country',
                            hintStyle: GoogleFonts.poppins(
                              color: _selectedCountry != null
                                  ? AppTheme.textPrimary
                                  : AppTheme.textSecondary.withOpacity(0.7),
                              fontSize: 14,
                            ),
                            filled: true,
                            fillColor: Colors.white,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide:
                                  BorderSide(color: AppTheme.borderColor),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide:
                                  BorderSide(color: AppTheme.borderColor),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                  color: AppTheme.accentColor, width: 1.5),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 16),
                            prefixIcon: Icon(
                              Icons.flag_rounded,
                              color: AppTheme.textSecondary,
                              size: 20,
                            ),
                            suffixIcon: _selectedCountry != null
                                ? IconButton(
                                    icon: Icon(
                                      Icons.close,
                                      color: AppTheme.textSecondary,
                                      size: 20,
                                    ),
                                    onPressed: () {
                                      setState(() {
                                        _selectedCountry = null;
                                        _fetchInitialWebsites();
                                      });
                                    },
                                  )
                                : Icon(
                                    Icons.arrow_drop_down,
                                    color: AppTheme.textSecondary,
                                  ),
                          ),
                          onTap: () {
                            showCountryPicker(
                              context: context,
                              showPhoneCode: false,
                              customFlagBuilder: (country) => Container(
                                margin: const EdgeInsets.only(right: 8),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(4),
                                  child: Image.network(
                                    'https://flagcdn.com/w40/${country.countryCode.toLowerCase()}.png',
                                    width: 30,
                                    height: 20,
                                    fit: BoxFit.cover,
                                    errorBuilder:
                                        (context, error, stackTrace) =>
                                            Text(country.flagEmoji),
                                  ),
                                ),
                              ),
                              countryListTheme: CountryListThemeData(
                                borderRadius: BorderRadius.circular(10),
                                inputDecoration: InputDecoration(
                                  hintText: 'Search country',
                                  filled: true,
                                  fillColor: AppTheme.componentBackColor,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: BorderSide.none,
                                  ),
                                ),
                              ),
                              onSelect: (Country country) {
                                setState(() {
                                  _selectedCountry = country.name;
                                  _fetchInitialWebsites();
                                });
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  _buildFilterGroup(
                    'Spam Score',
                    [
                      SizedBox(
                        width:
                            _getFieldWidth(constraints.maxWidth, factor: 0.75),
                        child: _buildNumberField('Min', (value) {
                          _minSpamScore = double.tryParse(value);
                          if (mounted) {
                            _fetchInitialWebsites();
                          }
                        }),
                      ),
                      const SizedBox(width: 12),
                      SizedBox(
                        width:
                            _getFieldWidth(constraints.maxWidth, factor: 0.75),
                        child: _buildNumberField('Max', (value) {
                          _maxSpamScore = double.tryParse(value);
                          if (mounted) {
                            _fetchInitialWebsites();
                          }
                        }),
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 28),
          Container(
            margin: const EdgeInsets.only(top: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _expandableController.toggle();
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: AppTheme.accentColor,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(color: AppTheme.accentColor),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    'Cancel',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w500,
                      fontSize: 15,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    // boxShadow: [
                    //   BoxShadow(
                    //     color: AppTheme.accentColor.withOpacity(0.3),
                    //     blurRadius: 12,
                    //     offset: const Offset(0, 4),
                    //     spreadRadius: 0,
                    //   ),
                    // ],
                  ),
                  child: ElevatedButton(
                    onPressed: () {
                      _fetchInitialWebsites();
                      setState(() {
                        _expandableController.toggle();
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 32, vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                      backgroundColor: Colors.transparent,
                      foregroundColor: Colors.white,
                      shadowColor: Colors.transparent,
                    ),
                    child: Ink(
                      decoration: BoxDecoration(
                        gradient: AppTheme.primaryGradient,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        child: Text(
                          'Apply Filters',
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.w600,
                            fontSize: 15,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterGroup(String title, List<Widget> children) {
    return Container(
      width: 280,
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(bottom: 10),
            child: Row(
              children: [
                Container(
                  width: 4,
                  height: 16,
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: children,
          ),
        ],
      ),
    );
  }

  Widget _buildNumberField(String label, Function(String) onChanged) {
    return TextField(
      decoration: InputDecoration(
        hintText: label,
        hintStyle: GoogleFonts.poppins(
          color: AppTheme.textSecondary.withOpacity(0.7),
          fontSize: 14,
        ),
        filled: true,
        fillColor: Colors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppTheme.borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppTheme.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppTheme.accentColor, width: 1.5),
        ),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        prefixIcon: label.contains('Min')
            ? Icon(Icons.remove, size: 18, color: AppTheme.textSecondary)
            : label.contains('Max')
                ? Icon(Icons.add, size: 18, color: AppTheme.textSecondary)
                : null,
      ),
      style: GoogleFonts.poppins(
        fontSize: 14,
        color: AppTheme.textPrimary,
        fontWeight: FontWeight.w500,
      ),
      keyboardType: TextInputType.number,
      onChanged: (value) {
        if (mounted) {
          setState(() {
            onChanged(value);
          });
        }
      },
    );
  }

  double _getFieldWidth(double maxWidth,
      {bool isDropdown = false, double factor = 1.2}) {
    double baseWidth;
    if (maxWidth >= 1200) {
      baseWidth = isDropdown ? 200.0 : 150.0;
    } else if (maxWidth >= 800) {
      baseWidth = isDropdown ? 180.0 : 130.0;
    } else if (maxWidth >= 600) {
      baseWidth = isDropdown ? 160.0 : 120.0;
    } else {
      baseWidth = maxWidth / 2 - 24;
    }
    return (baseWidth * factor).clamp(80.0, double.infinity);
  }

  Query _buildFirestoreQuery() {
    Query query = _websitesCollection
        .where('status', isEqualTo: 'Approved') // Fetch active websites
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true);

    if (_searchQuery.isNotEmpty) {
      query = query
          .where('domainName',
              isGreaterThanOrEqualTo: _searchQuery.toLowerCase())
          .where('domainName', isLessThanOrEqualTo: '$_searchQuery\uf8ff');
    }

    if (_selectedCategory != null &&
        _selectedCategory!.isNotEmpty &&
        _selectedCategory != 'All') {
      query = query.where('categories', arrayContains: _selectedCategory);
    }

    if (_minDA != null && _minDA! > 0) {
      query = query.where('da', isGreaterThanOrEqualTo: _minDA);
    }
    if (_maxDA != null && _maxDA! > 0) {
      query = query.where('da', isLessThanOrEqualTo: _maxDA);
    }
    if (_minDR != null && _minDR! > 0) {
      query = query.where('dr', isGreaterThanOrEqualTo: _minDR);
    }
    if (_maxDR != null && _maxDR! > 0) {
      query = query.where('dr', isLessThanOrEqualTo: _maxDR);
    }
    if (_minTraffic != null && _minTraffic! > 0) {
      query = query.where('traffic', isGreaterThanOrEqualTo: _minTraffic);
    }
    if (_maxTraffic != null && _maxTraffic! > 0) {
      query = query.where('traffic', isLessThanOrEqualTo: _maxTraffic);
    }
    if (_minPrice != null && _minPrice! > 0) {
      query = query.where('pricing', isGreaterThanOrEqualTo: _minPrice);
    }
    if (_maxPrice != null && _maxPrice! > 0) {
      query = query.where('pricing', isLessThanOrEqualTo: _maxPrice);
    }
    if (_selectedLanguage != null && _selectedLanguage!.isNotEmpty) {
      query = query.where('language', isEqualTo: _selectedLanguage);
    }

    if (_selectedCountry != null && _selectedCountry!.isNotEmpty) {
      query = query.where('country', isEqualTo: _selectedCountry);
    }

    if (_minSpamScore != null && _minSpamScore! > 0) {
      query = query.where('spamScore', isGreaterThanOrEqualTo: _minSpamScore);
    }
    if (_maxSpamScore != null && _maxSpamScore! > 0) {
      query = query.where('spamScore', isLessThanOrEqualTo: _maxSpamScore);
    }

    return query.limit(_pageSize);
  }

  Widget _buildWebsiteList() {
    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              if (index == _allWebsites.length) {
                return _buildLoadingIndicator();
              }
              final doc = _allWebsites[index];
              final data = doc.data() as Map<String, dynamic>;
              return _buildWebsiteCard(data, doc.id);
            },
            childCount:
                _allWebsites.length + (_hasMoreData || _isLoadingMore ? 1 : 0),
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingIndicator() {
    if (_isLoadingMore) {
      return const Padding(
        padding: EdgeInsets.all(8.0),
        child: Center(
            child: SizedBox(
                width: 100, height: 300, child: CircularProgressIndicator())),
      );
    }
    if (!_hasMoreData) {
      return const Padding(
        padding: EdgeInsets.all(8.0),
        child: Center(child: Text('No more websites to load')),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildWebsiteCard(Map<String, dynamic> data, String docId) {
    try {
      return Container(
        margin: const EdgeInsets.only(bottom: 24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppTheme.borderColor),
          boxShadow: [
            BoxShadow(
              color: AppTheme.cardShadow.withOpacity(0.08),
              blurRadius: 20,
              offset: const Offset(0, 8),
              spreadRadius: 2,
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(28.0),
          child: LayoutBuilder(
            builder: (context, constraints) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color:
                                        AppTheme.accentColor.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    Icons.language_rounded,
                                    color: AppTheme.accentColor,
                                    size: 20,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                InkWell(
                                  onTap: () =>
                                      launchUrl(Uri(host: data['url'])),
                                  child: Text(
                                    data['url']?.toString() ?? 'N/A',
                                    style: GoogleFonts.poppins(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 20,
                                      color: AppTheme.accentColor,
                                    ),
                                  ),
                                ),
                                if (data['adminWebsite'] == true) ...[
                                  const SizedBox(width: 12),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 4),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          Colors.amber.shade600,
                                          Colors.amber.shade400,
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(Icons.verified_rounded,
                                            color: Colors.white, size: 16),
                                        const SizedBox(width: 4),
                                        Text(
                                          'Verified',
                                          style: GoogleFonts.poppins(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                                SizedBox(
                                  width: 8,
                                ),
                                Wrap(
                                  spacing: 8,
                                  runSpacing: 8,
                                  children: (data['categories'] as List? ?? [])
                                      .take(3)
                                      .map((category) => Container(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 12, vertical: 6),
                                            decoration: BoxDecoration(
                                              color:
                                                  AppTheme.componentBackColor,
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            child: Text(
                                              category.toString(),
                                              style: GoogleFonts.poppins(
                                                fontSize: 13,
                                                fontWeight: FontWeight.w500,
                                                color: AppTheme.textPrimary,
                                              ),
                                            ),
                                          ))
                                      .toList(),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          // Container(
                          //   padding: const EdgeInsets.symmetric(
                          //       horizontal: 16, vertical: 10),
                          //   decoration: BoxDecoration(
                          //     color: Colors.green.shade50,
                          //     borderRadius: BorderRadius.circular(12),
                          //     border: Border.all(color: Colors.green.shade100),
                          //   ),
                          //   child: Row(
                          //     mainAxisSize: MainAxisSize.min,
                          //     children: [
                          //       Icon(Icons.attach_money_rounded,
                          //           size: 18, color: Colors.green.shade700),
                          //       const SizedBox(width: 6),
                          //       Text(
                          //         'Content placement',
                          //         style: GoogleFonts.poppins(
                          //           fontSize: 13,
                          //           fontWeight: FontWeight.w500,
                          //           color: Colors.green.shade800,
                          //         ),
                          //       ),
                          //       const SizedBox(width: 8),
                          //       Text(
                          //         '\$${data['basePricing']?.toStringAsFixed(2) ?? '0.00'}',
                          //         style: GoogleFonts.poppins(
                          //           fontSize: 16,
                          //           fontWeight: FontWeight.w600,
                          //           color: Colors.green.shade700,
                          //         ),
                          //       ),
                          //     ],
                          //   ),
                          // ),
                          //        const SizedBox(height: 16),
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: AppTheme.accentColor.withOpacity(0.2),
                                  blurRadius: 15,
                                  offset: const Offset(0, 0),
                                  spreadRadius: 1,
                                ),
                              ],
                            ),
                            child: ElevatedButton(
                              onPressed: () {
                                showDialog(
                                  barrierDismissible: false,
                                  useRootNavigator: true,
                                  context: context,
                                  builder: (context) => Dialog(
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(16)),
                                    child: SizedBox(
                                      width: 800,
                                      child: Container(
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(16)),
                                        child: BuyPostPage(
                                            websiteData: data,
                                            websiteId: docId),
                                      ),
                                    ),
                                  ),
                                );
                              },
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 1, vertical: 1),
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12)),
                                elevation: 0,
                                backgroundColor: Colors.transparent,
                              ),
                              child: Ink(
                                decoration: BoxDecoration(
                                  gradient: AppTheme.primaryGradient,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 12),
                                  child: Text(
                                    'Buy Post',
                                    style: GoogleFonts.poppins(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 15,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Container(
                    height: 1,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppTheme.borderColor.withOpacity(0.1),
                          AppTheme.borderColor,
                          AppTheme.borderColor.withOpacity(0.1),
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Wrap(
                    spacing: 16,
                    runSpacing: 16,
                    children: [
                      _buildMetricItem(
                        'Writing & Placement',
                        '\$${((data['basePricing'] != null ? (data['basePricing'] as num).toDouble() : 0.0) * 1.5).toStringAsFixed(2)}',
                        icon: Icons.edit_rounded,
                        tooltip:
                            'Cost for both writing and placing the content',
                        highlight: true,
                      ),
                      _buildMetricItem(
                        'Ahrefs Organic Traffic',
                        _formatNumber(data['traffic']),
                        icon: Icons.trending_up_rounded,
                        highlight: true,
                        tooltip:
                            'Organic traffic to the site as reported by Ahrefs',
                      ),
                      _buildMetricItem(
                        'Moz DA',
                        data['da']?.toString() ?? '0',
                        icon: Icons.bar_chart_rounded,
                        tooltip: 'Moz Domain Authority score (0-100)',
                      ),
                      _buildMetricItem(
                        'Ahrefs DR',
                        data['dr']?.toString() ?? '0',
                        icon: Icons.bar_chart_rounded,
                        tooltip: 'Ahrefs Domain Rating (0-100)',
                      ),
                      _buildMetricItem(
                        'Language',
                        data['language']?.toString() ?? 'N/A',
                        icon: Icons.language_rounded,
                        tooltip: 'Primary language of the website',
                      ),
                      _buildCountryMetricItem(
                        context,
                        'Country',
                        data['country']?.toString() ?? 'N/A',
                        tooltip: 'Primary country of the website\'s audience',
                      ),
                      _buildMetricItem(
                        'Links',
                        data['backlinkType']?.toString() ?? 'Dofollow',
                        icon: Icons.link_rounded,
                        highlight:
                            data['backlinkType']?.toString() == 'Dofollow',
                        tooltip:
                            'Type of backlinks provided (Dofollow/Nofollow)',
                      ),
                      _buildMetricItem(
                        'TAT',
                        'Up to 4 days',
                        icon: Icons.access_time_rounded,
                        tooltip: 'Turnaround Time for content placement',
                      ),
                      _buildMetricItem(
                        'Required content size',
                        'from ${data['wordCountMin']?.toString() ?? '1000'} words',
                        icon: Icons.description_rounded,
                        tooltip: 'Minimum required word count for content',
                      ),
                      _buildMetricItem(
                        'Marked "Sponsored"',
                        data['isSponsored']?.toString() == 'true'
                            ? 'Yes'
                            : 'No',
                        icon: Icons.label_rounded,
                        tooltip: 'Whether the post will be marked as sponsored',
                        highlight: data['isSponsored']?.toString() != 'true',
                      ),
                      _buildMetricItem(
                        'Spam Score',
                        '${data['spamScore']?.toString() ?? '0'}%',
                        icon: Icons.security_rounded,
                        tooltip: 'Spam score of the website (lower is better)',
                        highlight: (data['spamScore'] != null &&
                            double.tryParse(data['spamScore'].toString()) !=
                                null &&
                            double.parse(data['spamScore'].toString()) < 5),
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
        ),
      );
    } catch (e) {
      // Error handled silently
      return Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppTheme.borderColor),
          boxShadow: [
            BoxShadow(
              color: AppTheme.cardShadow.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Text(
          'Error rendering website card',
          style: GoogleFonts.poppins(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    }
  }

  String _formatNumber(dynamic number) {
    if (number == null) return '0';

    try {
      double value = double.parse(number.toString());
      if (value >= 1000000) {
        return '${(value / 1000000).toStringAsFixed(1)}M';
      } else if (value >= 1000) {
        return '${(value / 1000).toStringAsFixed(1)}K';
      } else {
        return value.toStringAsFixed(0);
      }
    } catch (e) {
      return number.toString();
    }
  }

  Widget _buildMetricItem(
    String title,
    String value, {
    IconData? icon,
    bool highlight = false,
    String? tooltip,
    Widget? customValueWidget,
  }) {
    return Tooltip(
      message: tooltip ?? '',
      preferBelow: true,
      textStyle: GoogleFonts.poppins(
        color: Colors.white,
        fontSize: 12,
        fontWeight: FontWeight.w500,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.accentGradientStart.withOpacity(0.9),
            AppTheme.accentGradientEnd.withOpacity(0.9),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: AppTheme.cardShadow.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      child: Container(
        width: 180,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppTheme.borderColor),
          boxShadow: [
            BoxShadow(
              color: AppTheme.cardShadow.withOpacity(0.05),
              blurRadius: 12,
              offset: const Offset(0, 4),
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (icon != null) ...[
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: highlight
                          ? AppTheme.accentColor.withOpacity(0.1)
                          : AppTheme.lightGrey,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      icon,
                      size: 16,
                      color: highlight
                          ? AppTheme.accentColor
                          : AppTheme.textSecondary,
                    ),
                  ),
                  const SizedBox(width: 10),
                ],
                Expanded(
                  child: Text(
                    title,
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textSecondary,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            customValueWidget ??
                Text(
                  value,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color:
                        highlight ? AppTheme.accentColor : AppTheme.textPrimary,
                    letterSpacing: -0.2,
                  ),
                ),
          ],
        ),
      ),
    );
  }

  // Build a metric item with country flag
  Widget _buildCountryMetricItem(
    BuildContext context,
    String title,
    String countryName, {
    bool highlight = false,
    String? tooltip,
  }) {
    // Get country code from country name (2-letter code)
    String countryCode = '';
    try {
      // Try to find the country in the country picker list
      final countries = CountryService().getAll();
      final country = countries.firstWhere(
        (c) => c.name.toLowerCase() == countryName.toLowerCase(),
        orElse: () => Country(
          phoneCode: '',
          countryCode: '',
          e164Sc: 0,
          geographic: true,
          level: 1,
          name: countryName,
          example: '',
          displayName: countryName,
          displayNameNoCountryCode: countryName,
          e164Key: '',
        ),
      );
      countryCode = country.countryCode.toLowerCase();
    } catch (e) {
      // If country not found, use empty code
      countryCode = '';
    }

    Widget countryWidget = Row(
      children: [
        if (countryCode.isNotEmpty) ...[
          ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Image.network(
              'https://flagcdn.com/w40/$countryCode.png',
              width: 24,
              height: 16,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => const Icon(
                Icons.flag_rounded,
                size: 16,
                color: Colors.grey,
              ),
            ),
          ),
          const SizedBox(width: 8),
        ],
        Expanded(
          child: Text(
            countryName,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: highlight ? AppTheme.accentColor : AppTheme.textPrimary,
              letterSpacing: -0.2,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );

    return _buildMetricItem(
      title,
      '', // Empty value as we're using a custom widget
      icon: Icons.flag_rounded,
      tooltip: tooltip,
      customValueWidget: countryWidget,
    );
  }

  // Removed unused methods (_buildErrorWidget, _buildClientSideFilteredTable, _showWebsiteDetails)
  // as they were not critical to the core functionality and can be re-added if needed.
}
