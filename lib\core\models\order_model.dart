import 'package:cloud_firestore/cloud_firestore.dart';

class OrderModel {
  final String? orderId;
  final String websiteId;
  final String buyerId;
  final String websiteUrl;
  final String websiteDomainName;
  final String websiteLanguage;
  final List<String> websiteCategories;
  final String postTitle;
  final String postContent;
  final int wordCount;
  final List<String> links;
  final String backlinkType;
  final bool isSponsored;
  final double basePrice;
  final double specialTopicsAdditionalPrice;
  final double totalPrice;
  final String status;
  final Timestamp orderDate;
  final Timestamp? lastUpdated;

  final Timestamp? approvalDate;
  final Timestamp? completionDate;
  final String paymentStatus;
  final String? paymentId;
  final String? notes;
  final String publisherId;
  final String? rejectionReason;
  final bool? isDisputed;
  final String? disputeNote;
  final String? disputeStatus; // Open, In Review, Resolved
  final String?
      actionBy; // Who performed the last status change (publisher, buyer, admin)
  final Timestamp? actionTimestamp; // When the last status change occurred
  final Timestamp? inProgressDate; // When the order was marked as In Progress

  OrderModel({
    this.orderId,
    required this.websiteId,
    required this.buyerId,
    required this.websiteUrl,
    required this.websiteDomainName,
    required this.websiteLanguage,
    required this.websiteCategories,
    required this.postTitle,
    required this.postContent,
    required this.wordCount,
    required this.links,
    required this.backlinkType,
    required this.isSponsored,
    required this.basePrice,
    required this.specialTopicsAdditionalPrice,
    required this.totalPrice,
    required this.status,
    required this.orderDate,
    this.lastUpdated,
    this.approvalDate,
    this.completionDate,
    required this.paymentStatus,
    this.paymentId,
    this.notes,
    required this.publisherId,
    this.rejectionReason,
    this.isDisputed,
    this.disputeNote,
    this.disputeStatus,
    this.actionBy,
    this.actionTimestamp,
    this.inProgressDate,
  });

  factory OrderModel.fromMap(Map<String, dynamic> map) {
    return OrderModel(
      orderId: map['orderId'],
      websiteId: map['websiteId'],
      buyerId: map['buyerId'],
      websiteUrl: map['websiteUrl'],
      websiteDomainName: map['websiteDomainName'],
      websiteLanguage: map['websiteLanguage'],
      websiteCategories: List<String>.from(map['websiteCategories']),
      postTitle: map['postTitle'],
      postContent: map['postContent'],
      wordCount: map['wordCount'],
      links: List<String>.from(map['links']),
      backlinkType: map['backlinkType'],
      isSponsored: map['isSponsored'],
      basePrice: (map['basePrice'] as num).toDouble(),
      specialTopicsAdditionalPrice:
          (map['specialTopicsAdditionalPrice'] as num).toDouble(),
      totalPrice: (map['totalPrice'] as num).toDouble(),
      status: map['status'],
      orderDate: map['orderDate'],
      lastUpdated: map['lastUpdated'],
      approvalDate: map['approvalDate'],
      completionDate: map['completionDate'],
      paymentStatus: map['paymentStatus'],
      paymentId: map['paymentId'],
      notes: map['notes'],
      publisherId: map['publisherId'],
      rejectionReason: map['rejectionReason'],
      isDisputed: map['isDisputed'],
      disputeNote: map['disputeNote'],
      disputeStatus: map['disputeStatus'],
      actionBy: map['actionBy'],
      actionTimestamp: map['actionTimestamp'],
      inProgressDate: map['inProgressDate'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'orderId': orderId,
      'websiteId': websiteId,
      'buyerId': buyerId,
      'websiteUrl': websiteUrl,
      'websiteDomainName': websiteDomainName,
      'websiteLanguage': websiteLanguage,
      'websiteCategories': websiteCategories,
      'postTitle': postTitle,
      'postContent': postContent,
      'wordCount': wordCount,
      'links': links,
      'backlinkType': backlinkType,
      'isSponsored': isSponsored,
      'basePrice': basePrice,
      'specialTopicsAdditionalPrice': specialTopicsAdditionalPrice,
      'totalPrice': totalPrice,
      'status': status,
      'orderDate': orderDate,
      'lastUpdated': lastUpdated,
      'approvalDate': approvalDate,
      'completionDate': completionDate,
      'paymentStatus': paymentStatus,
      'paymentId': paymentId,
      'notes': notes,
      'publisherId': publisherId,
      'rejectionReason': rejectionReason,
      'isDisputed': isDisputed,
      'disputeNote': disputeNote,
      'disputeStatus': disputeStatus,
      'actionBy': actionBy,
      'actionTimestamp': actionTimestamp,
      'inProgressDate': inProgressDate,
    };
  }
}
