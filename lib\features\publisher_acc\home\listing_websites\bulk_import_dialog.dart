import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:csv/csv.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:guest_posts/core/models/website_model.dart';
import 'package:guest_posts/core/theme/app_theme.dart';
import 'package:guest_posts/core/utils/colors.dart';
import 'package:guest_posts/core/utils/csv_helper.dart';
import 'package:guest_posts/core/utils/toast_helper.dart';

class BulkImportDialog extends StatefulWidget {
  const BulkImportDialog({super.key});

  @override
  State<BulkImportDialog> createState() => _BulkImportDialogState();
}

class _BulkImportDialogState extends State<BulkImportDialog> {
  bool _isLoading = false;
  bool _fileSelected = false;
  bool _validationComplete = false;
  String _fileName = '';
  List<Map<String, dynamic>> _parsedData = [];
  List<Map<String, dynamic>> _validationResults = [];
  int _validCount = 0;
  int _invalidCount = 0;
  int _uploadedCount = 0;
  bool _isUploading = false;

  Future<void> _pickCSVFile() async {
    setState(() {
      _isLoading = true;
      _fileSelected = false;
      _validationComplete = false;
      _parsedData = [];
      _validationResults = [];
    });

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (result != null) {
        final bytes = result.files.single.bytes;
        final name = result.files.single.name;

        if (bytes != null) {
          final csvString = utf8.decode(bytes);
          final parsedData = CSVHelper.parseCSV(csvString);

          setState(() {
            _fileName = name;
            _fileSelected = true;
            _parsedData = parsedData;
          });

          _validateData();
        }
      }
    } catch (e) {
      ToastHelper.showError('Error picking file: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _validateData() {
    if (_parsedData.isEmpty) return;

    List<Map<String, dynamic>> results = [];
    int validCount = 0;
    int invalidCount = 0;

    for (var data in _parsedData) {
      final validationResult = CSVHelper.validateWebsiteData(data);
      results.add(validationResult);

      if (validationResult['isValid']) {
        validCount++;
      } else {
        invalidCount++;
      }
    }

    setState(() {
      _validationResults = results;
      _validCount = validCount;
      _invalidCount = invalidCount;
      _validationComplete = true;
    });
  }

  Future<void> _uploadWebsites() async {
    if (_validCount == 0) {
      ToastHelper.showError('No valid websites to upload');
      return;
    }

    setState(() {
      _isUploading = true;
      _uploadedCount = 0;
    });

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        ToastHelper.showError('User not authenticated');
        return;
      }

      final batch = FirebaseFirestore.instance.batch();
      final validWebsites =
          _validationResults.where((result) => result['isValid']).toList();

      for (var result in validWebsites) {
        final websiteData = result['data'];
        final website = CSVHelper.convertToWebsiteModel(websiteData, user.uid);

        final docRef = FirebaseFirestore.instance.collection('websites').doc();
        batch.set(docRef, {
          ...website.toMap(),
          'websiteId': docRef.id,
          'submissionDate': FieldValue.serverTimestamp(),
          'createdAt': FieldValue.serverTimestamp(),
          'lastUpdated': FieldValue.serverTimestamp(),
        });

        setState(() {
          _uploadedCount++;
        });
      }

      await batch.commit();

      ToastHelper.showSuccess('Successfully uploaded $_uploadedCount websites');
      Navigator.of(context).pop(true); // Return true to indicate success
    } catch (e) {
      ToastHelper.showError('Error uploading websites: $e');
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  void _downloadTemplate() {
    CSVHelper.downloadCSVTemplate();
    ToastHelper.showSuccess('Template downloaded');
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.6,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Bulk Import Websites',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.accentColor,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),

            // Instructions
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Instructions:',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '1. Download the CSV template',
                    style: TextStyle(fontFamily: 'Cairo'),
                  ),
                  const Text(
                    '2. Fill in your website details in the template',
                    style: TextStyle(fontFamily: 'Cairo'),
                  ),
                  const Text(
                    '3. Upload the completed CSV file',
                    style: TextStyle(fontFamily: 'Cairo'),
                  ),
                  const Text(
                    '4. Validate and import your websites',
                    style: TextStyle(fontFamily: 'Cairo'),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.download),
                    label: const Text('Download Template'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.accentColor,
                      foregroundColor: Colors.white,
                    ),
                    onPressed: _downloadTemplate,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // File Upload Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Upload CSV File',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.accentColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  if (!_fileSelected)
                    Center(
                      child: InkWell(
                        onTap: _isLoading ? null : _pickCSVFile,
                        child: Container(
                          width: double.infinity,
                          height: 120,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.grey.shade300,
                              style: BorderStyle.solid,
                            ),
                          ),
                          child: _isLoading
                              ? const Center(child: CircularProgressIndicator())
                              : Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: const [
                                    Icon(Icons.upload_file,
                                        size: 48, color: Colors.grey),
                                    SizedBox(height: 8),
                                    Text(
                                      'Click to select CSV file',
                                      style: TextStyle(
                                        fontFamily: 'Cairo',
                                        color: Colors.grey,
                                      ),
                                    ),
                                  ],
                                ),
                        ),
                      ),
                    )
                  else
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.file_present,
                                color: AppTheme.accentColor),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _fileName,
                                style: const TextStyle(fontFamily: 'Cairo'),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            TextButton(
                              onPressed: _isLoading ? null : _pickCSVFile,
                              child: const Text('Change File'),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        if (_validationComplete)
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: _invalidCount > 0
                                  ? Colors.orange.shade50
                                  : Colors.green.shade50,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Validation Results:',
                                  style: TextStyle(
                                    fontFamily: 'Cairo',
                                    fontWeight: FontWeight.bold,
                                    color: _invalidCount > 0
                                        ? Colors.orange.shade800
                                        : Colors.green.shade800,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Total Websites: ${_parsedData.length}',
                                  style: const TextStyle(fontFamily: 'Cairo'),
                                ),
                                Text(
                                  'Valid: $_validCount',
                                  style: TextStyle(
                                    fontFamily: 'Cairo',
                                    color: Colors.green.shade800,
                                  ),
                                ),
                                if (_invalidCount > 0)
                                  Text(
                                    'Invalid: $_invalidCount',
                                    style: TextStyle(
                                      fontFamily: 'Cairo',
                                      color: Colors.red.shade800,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                      ],
                    ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Action Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed:
                      _isUploading ? null : () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed:
                      _validationComplete && _validCount > 0 && !_isUploading
                          ? _uploadWebsites
                          : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.accentColor,
                    foregroundColor: Colors.white,
                  ),
                  child: _isUploading
                      ? Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text('Uploading ($_uploadedCount/$_validCount)'),
                          ],
                        )
                      : const Text('Import Websites'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
