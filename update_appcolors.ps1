$filePath = "lib\features\buyer_acc\find_publisher.dart"
$content = Get-Content $filePath -Raw

# Replace all AppColors references with AppTheme
$content = $content -replace "AppColors\.backgroundColor", "AppTheme.backgroundColor"
$content = $content -replace "AppColors\.accentColor", "AppTheme.accentColor"
$content = $content -replace "AppColors\.textPrimary", "AppTheme.textPrimary"
$content = $content -replace "AppColors\.textSecondary", "AppTheme.textSecondary"
$content = $content -replace "AppColors\.borderColor", "AppTheme.borderColor"
$content = $content -replace "AppColors\.lightGrey", "AppTheme.lightGrey"
$content = $content -replace "AppColors\.cardShadow", "AppTheme.cardShadow"
$content = $content -replace "AppColors\.primaryGradient", "AppTheme.primaryGradient"
$content = $content -replace "AppColors\.componentBackColor", "AppTheme.componentBackColor"
$content = $content -replace "AppColors\.accentGradientStart", "AppTheme.accentGradientStart"
$content = $content -replace "AppColors\.accentGradientEnd", "AppTheme.accentGradientEnd"

# Save the updated content back to the file
$content | Set-Content $filePath -NoNewline
