import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts/core/theme/app_theme.dart';

/// A premium styled app bar with consistent styling
class PremiumAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final bool hasBadge;
  final Color? backgroundColor;
  final double elevation;
  final double height;

  const PremiumAppBar({
    Key? key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = false,
    this.showBackButton = true,
    this.onBackPressed,
    this.hasBadge = false,
    this.backgroundColor,
    this.elevation = 0,
    this.height = kToolbarHeight,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: hasBadge
          ? Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacingS,
                    vertical: AppTheme.spacingXS,
                  ),
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusS),
                  ),
                  child: Text(
                    'PREMIUM',
                    style: GoogleFonts.poppins(
                      fontSize: AppTheme.fontSizeXS,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: AppTheme.spacingS),
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: AppTheme.fontSizeL,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ],
            )
          : Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: AppTheme.fontSizeL,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimary,
              ),
            ),
      centerTitle: centerTitle,
      leading: showBackButton && Navigator.of(context).canPop()
          ? IconButton(
              icon: const Icon(Icons.arrow_back_ios_rounded, size: 20),
              onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
              color: AppTheme.textPrimary,
            )
          : leading,
      actions: actions,
      backgroundColor: backgroundColor ?? AppTheme.surfaceColor,
      elevation: elevation,
      toolbarHeight: height,
      shape: elevation > 0
          ? null
          : Border(
              bottom: BorderSide(
                color: AppTheme.borderColor,
                width: 1,
              ),
            ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height);
}
