# Get all Dart files in the project
$dartFiles = Get-ChildItem -Path "lib" -Filter "*.dart" -Recurse

foreach ($file in $dartFiles) {
    Write-Host "Processing $($file.FullName)"
    $content = Get-Content $file.FullName -Raw
    
    # Check if the file contains AppColors references
    if ($content -match "AppColors\.") {
        Write-Host "Found AppColors references in $($file.Name)"
        
        # Add import if it doesn't exist
        if (-not ($content -match "import 'package:guest_posts/core/theme/app_theme.dart';")) {
            # Find the last import line
            $lastImportIndex = $content.LastIndexOf("import ")
            $lastImportEndIndex = $content.IndexOf(";", $lastImportIndex) + 1
            
            # Insert the new import after the last import
            $newImport = "`nimport 'package:guest_posts/core/theme/app_theme.dart';"
            $content = $content.Insert($lastImportEndIndex, $newImport)
        }
        
        # Replace all AppColors references with AppTheme
        $content = $content -replace "AppColors\.backgroundColor", "AppTheme.backgroundColor"
        $content = $content -replace "AppColors\.accentColor", "AppTheme.accentColor"
        $content = $content -replace "AppColors\.textPrimary", "AppTheme.textPrimary"
        $content = $content -replace "AppColors\.textSecondary", "AppTheme.textSecondary"
        $content = $content -replace "AppColors\.borderColor", "AppTheme.borderColor"
        $content = $content -replace "AppColors\.lightGrey", "AppTheme.lightGrey"
        $content = $content -replace "AppColors\.cardShadow", "AppTheme.cardShadow"
        $content = $content -replace "AppColors\.primaryGradient", "AppTheme.primaryGradient"
        $content = $content -replace "AppColors\.componentBackColor", "AppTheme.componentBackColor"
        $content = $content -replace "AppColors\.accentGradientStart", "AppTheme.accentGradientStart"
        $content = $content -replace "AppColors\.accentGradientEnd", "AppTheme.accentGradientEnd"
        $content = $content -replace "AppColors\.greyBack", "AppTheme.lightGrey"
        $content = $content -replace "AppColors\.greyText", "AppTheme.textSecondary"
        $content = $content -replace "AppColors\.secondryColor", "AppTheme.secondaryColor"
        $content = $content -replace "AppColors\.clickBlue", "AppTheme.accentColor"
        $content = $content -replace "AppColors\.primaryColor", "AppTheme.primaryColor"
        $content = $content -replace "AppColors\.primaryButtonColor", "AppTheme.primaryColor"
        $content = $content -replace "AppColors\.black", "AppTheme.textPrimary"
        
        # Save the updated content back to the file
        $content | Set-Content $file.FullName -NoNewline
        Write-Host "Updated $($file.Name)"
    }
}
