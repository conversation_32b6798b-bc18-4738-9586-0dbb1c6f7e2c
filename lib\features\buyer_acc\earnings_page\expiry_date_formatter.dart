import 'package:flutter/services.dart';

class ExpiryDateInputF<PERSON>atter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final newText = newValue.text;
    
    if (newText.isEmpty) {
      return newValue;
    }
    
    // Remove all non-digits
    String digitsOnly = newText.replaceAll(RegExp(r'\D'), '');
    
    // Limit to 4 digits (MM/YY)
    if (digitsOnly.length > 4) {
      digitsOnly = digitsOnly.substring(0, 4);
    }
    
    // Format as MM/YY
    String formatted = '';
    
    if (digitsOnly.length >= 1) {
      // First digit of month can only be 0 or 1
      if (digitsOnly[0] != '0' && digitsOnly[0] != '1') {
        digitsOnly = '0' + digitsOnly;
      }
    }
    
    if (digitsOnly.length >= 2) {
      // If first digit is 1, second digit can only be 0, 1, or 2
      if (digitsOnly[0] == '1' && int.parse(digitsOnly[1]) > 2) {
        digitsOnly = '1' + '2' + digitsOnly.substring(2);
      }
    }
    
    // Format with slash
    if (digitsOnly.length <= 2) {
      formatted = digitsOnly;
    } else {
      formatted = digitsOnly.substring(0, 2) + '/' + digitsOnly.substring(2);
    }
    
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
