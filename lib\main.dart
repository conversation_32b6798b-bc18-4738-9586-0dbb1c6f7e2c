import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:guest_posts/app_router.dart';
import 'package:guest_posts/core/theme/app_theme.dart';
import 'package:guest_posts/firebase_options.dart';

// We don't need to import flutter_stripe for web since we're using a custom web implementation

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: kIsWeb ? DefaultFirebaseOptions.web : null,
  );

  // No need to initialize Stripe for web as we're using a custom implementation

  runApp(const MyApp());
}

class CustomScrollBehavior extends MaterialScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
        PointerDeviceKind.trackpad,
      };
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      scrollBehavior: CustomScrollBehavior(),
      theme: AppTheme.getTheme(),
      title: 'Guest Posts',
      routerConfig: AppRouter.router,
      debugShowCheckedModeBanner: false,
    );
  }
}
