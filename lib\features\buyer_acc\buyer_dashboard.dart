import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts/core/models/order_model.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:guest_posts/core/theme/app_theme.dart';
import 'package:guest_posts/core/utils/app_icons.dart';
import 'package:guest_posts/core/widgets/premium_widgets.dart';
import 'package:guest_posts/features/chat/chat_dialog.dart';
import 'package:guest_posts/core/utils/toast_helper.dart';
import 'package:intl/intl.dart';

class BuyerDashboard extends StatefulWidget {
  const BuyerDashboard({super.key});

  @override
  State<BuyerDashboard> createState() => _BuyerDashboardState();
}

class _BuyerDashboardState extends State<BuyerDashboard> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  LineChartBarData? lineChartBarData;
  List<Map<String, dynamic>> ordersData = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOrdersData();
  }

  Future<void> _loadOrdersData() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      if (mounted) setState(() => _isLoading = true);

      // Fetch orders where userId matches the current user
      final ordersSnapshot = await _firestore
          .collection('orders')
          .where('buyerId', isEqualTo: user.uid)
          .orderBy('orderDate', descending: true)
          .limit(30)
          .get();

      ordersData = ordersSnapshot.docs.map((doc) {
        final order = OrderModel.fromMap(doc.data());
        return {
          'date': order.orderDate.toDate(),
          'value': order.totalPrice,
          'status': order.status.toString().capitalizeFirstLetter(),
        };
      }).toList();

      final now = DateTime.now();
      final thirtyDaysAgo = now.subtract(const Duration(days: 30));
      if (mounted) {
        setState(() {
          lineChartBarData = LineChartBarData(
            spots: ordersData
                .where((data) => data['date'].isAfter(thirtyDaysAgo))
                .map((data) => FlSpot(
                      data['date'].difference(thirtyDaysAgo).inDays.toDouble(),
                      data['value'].toDouble(),
                    ))
                .toList(),
            isCurved: true,
            color: Colors.amber,
            barWidth: 2,
            isStrokeCapRound: true,
            dotData: FlDotData(show: true),
            belowBarData: BarAreaData(
              show: true,
              color: Colors.amber.withAlpha(100),
            ),
          );
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading orders: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<Map<String, dynamic>> _calculateMetrics() async {
    final user = _auth.currentUser;
    if (user == null) return {};

    final ordersSnapshot = await _firestore
        .collection('orders')
        .where('buyerId', isEqualTo: user.uid)
        .get();

    double totalSpent = 0;
    double reservedBalance = 0;
    int orders = 0;

    int completedOrders = 0;

    for (var doc in ordersSnapshot.docs) {
      orders++;
      final order = OrderModel.fromMap(doc.data());
      //  totalSpent += order.totalPrice;
      if (order.status == 'Pending') {
        reservedBalance += order.totalPrice;
        // Orders++;
      } else if (order.status == 'Completed') {
        completedOrders++;
      }
    }

    return {
      'totalSpent': totalSpent,
      'reservedBalance': reservedBalance,
      'Orders': orders,
      'completedOrders': completedOrders,
    };
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;
    final isMediumScreen = screenWidth >= 600 && screenWidth < 1200;

    if (_auth.currentUser == null) {
      return const Center(child: Text('Please sign in to view your dashboard'));
    }

    return StreamBuilder<DocumentSnapshot<Map<String, dynamic>>>(
      stream: _firestore
          .collection('users')
          .doc(_auth.currentUser!.uid)
          .snapshots(),
      builder: (context, userSnapshot) {
        if (userSnapshot.connectionState == ConnectionState.waiting ||
            _isLoading) {
          return const Center(child: CircularProgressIndicator());
        }
        if (!userSnapshot.hasData || userSnapshot.data?.data() == null) {
          return const Center(child: Text('User data not found'));
        }

        return FutureBuilder<Map<String, dynamic>>(
          future: _calculateMetrics(),
          builder: (context, metricsSnapshot) {
            if (metricsSnapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            final metrics = metricsSnapshot.data ?? {};
            final totalSpent = (metrics['totalSpent'] as double?) ?? 0.0;
            final reservedBalance =
                (metrics['reservedBalance'] as double?) ?? 0.0;
            final pendingOrders = (metrics['Orders'] as int?) ?? 0;
            final completedOrders = (metrics['completedOrders'] as int?) ?? 0;
            final buyerFunds = userSnapshot.data!['buyerFunds'] ?? 0.0;

            return Scaffold(
              backgroundColor: AppTheme.backgroundColor,
              body: SingleChildScrollView(
                child: Container(
                  padding: EdgeInsets.symmetric(
                      vertical: isSmallScreen ? 16.0 : 24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Dashboard Overview',
                              style: GoogleFonts.poppins(
                                fontSize: 24,
                                fontWeight: FontWeight.w600,
                                color: AppTheme.textPrimary,
                              ),
                            ),
                            const SizedBox(height: 6),
                            Text(
                              'Welcome back! Here\'s a summary of your account',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: AppTheme.textSecondary,
                              ),
                            ),
                            const SizedBox(height: 24),
                            GridView.count(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              crossAxisCount:
                                  isSmallScreen ? 2 : (isMediumScreen ? 4 : 4),
                              mainAxisSpacing: 16,
                              crossAxisSpacing: 16,
                              childAspectRatio: isSmallScreen ? 2 : 2,
                              children: [
                                PremiumMetricItem(
                                  title: 'Main Balance',
                                  value: '\$${buyerFunds.toStringAsFixed(2)}',
                                  icon: AppIcons.wallet,
                                  highlight: true,
                                  tooltip:
                                      'Your available balance for new orders',
                                  width: double.infinity,
                                  subtitle: 'Ready to spend',
                                  onTap: () {
                                    // Navigate to wallet page
                                  },
                                ),
                                PremiumMetricItem(
                                  title: 'Reserved Balance',
                                  value:
                                      '\$${reservedBalance.toStringAsFixed(2)}',
                                  icon: AppIcons.lock,
                                  tooltip: 'Funds reserved for pending orders',
                                  width: double.infinity,
                                  subtitle:
                                      'Pending orders money awaiting completion',
                                ),
                                PremiumMetricItem(
                                  title: 'Active Orders',
                                  value: pendingOrders.toString(),
                                  icon: AppIcons.receipt,
                                  tooltip: 'Orders awaiting completion',
                                  width: double.infinity,
                                  subtitle: 'Awaiting completion',
                                ),
                                PremiumMetricItem(
                                  title: 'Completed Orders',
                                  value: completedOrders.toString(),
                                  icon: AppIcons.success,
                                  tooltip: 'Successfully completed orders',
                                  width: double.infinity,
                                  subtitle: 'Completed orders',
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Recent Activity',
                              style: GoogleFonts.poppins(
                                fontSize: 22,
                                fontWeight: FontWeight.w600,
                                color: AppTheme.textPrimary,
                              ),
                            ),
                            const SizedBox(height: 6),
                            Text(
                              'Track your recent orders and their status',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: AppTheme.textSecondary,
                              ),
                            ),
                            const SizedBox(height: 16),
                            const OrdersTableWidget(),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}

// Assume OrderModel and AppColors are imported and implemented.
// If using String.capitalizeFirstLetter(), include the extension.

class OrdersTableWidget extends StatefulWidget {
  const OrdersTableWidget({super.key});

  @override
  State<OrdersTableWidget> createState() => _OrdersTableWidgetState();
}

class _OrdersTableWidgetState extends State<OrdersTableWidget> {
  String _selectedTab = 'All';
  final TextEditingController _searchController = TextEditingController();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  List<OrderModel> _orders = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  Future<void> _loadOrders() async {
    try {
      if (mounted) setState(() => _isLoading = true);
      final user = _auth.currentUser;
      if (user == null) return;

      final ordersSnapshot = await _firestore
          .collection('orders')
          .where('buyerId', isEqualTo: user.uid)
          .orderBy('orderDate', descending: true)
          .limit(50)
          .get();

      _orders = ordersSnapshot.docs
          .map((doc) => OrderModel.fromMap({
                ...doc.data(),
                'orderId': doc.id,
              }))
          .toList();

      if (mounted) setState(() => _isLoading = false);
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading orders: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<OrderModel> get _filteredOrders {
    if (_selectedTab == 'All') return _orders;
    return _orders
        .where((order) => order.status.capitalizeFirstLetter() == _selectedTab)
        .toList();
  }

  List<OrderModel> get _searchedOrders {
    if (_searchController.text.isEmpty) return _filteredOrders;
    final searchTerm = _searchController.text.toLowerCase();
    return _filteredOrders
        .where((order) =>
            order.orderId!.toLowerCase().contains(searchTerm) ||
            order.websiteDomainName.toLowerCase().contains(searchTerm))
        .toList();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  int _getTabCount(String tabName) {
    switch (tabName) {
      case 'All':
        return _orders.length;
      case 'Pending':
        return _orders
            .where((order) => order.status.capitalizeFirstLetter() == 'Pending')
            .length;
      case 'In Progress':
        return _orders
            .where((order) =>
                order.status.capitalizeFirstLetter() == 'In Progress')
            .length;
      case 'Completed':
        return _orders
            .where(
                (order) => order.status.capitalizeFirstLetter() == 'Completed')
            .length;
      case 'Cancelled':
        return _orders
            .where(
                (order) => order.status.capitalizeFirstLetter() == 'Cancelled')
            .length;
      default:
        return 0;
    }
  }

  Color _statusColor(String status) {
    switch (status) {
      case 'Completed':
        return Colors.green;
      case 'Pending':
        return Colors.orange;
      case 'In Progress':
        return Colors.purple;
      case 'Cancelled':
        return Colors.red;
      default:
        return Colors.blue;
    }
  }

  IconData _statusIcon(String status) {
    switch (status) {
      case 'Completed':
        return AppIcons.success;
      case 'Pending':
        return AppIcons.time;
      case 'In Progress':
        return Icons.sync;
      case 'Cancelled':
        return AppIcons.error;
      default:
        return AppIcons.help;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isWideScreen = MediaQuery.of(context).size.width > 800;

    return Container(
      //   padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        // boxShadow: [
        //   BoxShadow(
        //     color: AppTheme.cardShadow.withOpacity(0.08),
        //     blurRadius: 15,
        //     offset: const Offset(0, 5),
        //     spreadRadius: 2,
        //   ),
        // ],
        border: Border.all(color: AppTheme.borderColor.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: AppTheme.accentColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      AppIcons.receipt,
                      color: AppTheme.accentColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Recent Orders',
                    style: GoogleFonts.poppins(
                      fontSize: isWideScreen ? 20 : 18,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                ],
              ),
              SizedBox(
                width: isWideScreen ? 300 : 200,
                child: PremiumTextField(
                  controller: _searchController,
                  hintText: 'Search by website or order ID',
                  prefixIcon: Icon(AppIcons.search, size: 20),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  onChanged: (value) {
                    if (mounted) setState(() {});
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          DefaultTabController(
            length: 5,
            child: PremiumTabBar(
              tabs: const [
                'All',
                'Pending',
                'In Progress',
                'Completed',
                'Cancelled'
              ],
              selectedIndex: [
                'All',
                'Pending',
                'In Progress',
                'Completed',
                'Cancelled'
              ].indexOf(_selectedTab),
              onTabSelected: (index) {
                if (mounted) {
                  setState(() {
                    _selectedTab = [
                      'All',
                      'Pending',
                      'In Progress',
                      'Completed',
                      'Cancelled'
                    ][index];
                  });
                }
              },
              showCounts: true,
              counts: [
                _getTabCount('All'),
                _getTabCount('Pending'),
                _getTabCount('In Progress'),
                _getTabCount('Completed'),
                _getTabCount('Cancelled'),
              ],
            ),
          ),
          const SizedBox(height: 24),
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(30.0),
                child: CircularProgressIndicator(),
              ),
            )
          else if (_searchedOrders.isEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(40.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      AppIcons.receipt,
                      size: 64,
                      color: Colors.grey[300],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No orders found',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: AppTheme.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Try adjusting your search or filters',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: AppTheme.textLight,
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _searchedOrders.length,
              itemBuilder: (context, index) {
                final order = _searchedOrders[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12.0),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(14),
                      border: Border.all(
                          color: AppTheme.borderColor.withOpacity(0.2)),
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.cardShadow.withOpacity(0.14),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    clipBehavior: Clip.antiAlias,
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(14),
                        onTap: () {
                          _viewOrderDetails(order);
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              // STATUS ICON
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: _statusColor(
                                          order.status.capitalizeFirstLetter())
                                      .withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Icon(
                                  _statusIcon(
                                      order.status.capitalizeFirstLetter()),
                                  color: _statusColor(
                                      order.status.capitalizeFirstLetter()),
                                  size: 30,
                                ),
                              ),
                              const SizedBox(width: 16),
                              // INFO
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Text(
                                            order.websiteDomainName,
                                            style: GoogleFonts.poppins(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 16,
                                              color: AppTheme.textPrimary,
                                            ),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 10,
                                            vertical: 4,
                                          ),
                                          decoration: BoxDecoration(
                                            color: _statusColor(order.status
                                                    .capitalizeFirstLetter())
                                                .withOpacity(0.1),
                                            borderRadius:
                                                BorderRadius.circular(20),
                                          ),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Icon(
                                                _statusIcon(order.status
                                                    .capitalizeFirstLetter()),
                                                size: 14,
                                                color: _statusColor(order.status
                                                    .capitalizeFirstLetter()),
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                order.status
                                                    .capitalizeFirstLetter(),
                                                style: GoogleFonts.poppins(
                                                  fontWeight: FontWeight.w600,
                                                  fontSize: 12,
                                                  color: _statusColor(order
                                                      .status
                                                      .capitalizeFirstLetter()),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    Row(
                                      children: [
                                        Icon(
                                          AppIcons.tag,
                                          size: 14,
                                          color: AppTheme.textSecondary,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          "#${order.orderId}",
                                          style: GoogleFonts.poppins(
                                            fontSize: 13,
                                            color: AppTheme.textSecondary,
                                          ),
                                        ),
                                        const SizedBox(width: 16),
                                        Icon(
                                          AppIcons.calendar,
                                          size: 14,
                                          color: AppTheme.textSecondary,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          _formatDate(order.orderDate),
                                          style: GoogleFonts.poppins(
                                            fontSize: 13,
                                            color: AppTheme.textSecondary,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          children: [
                                            Icon(
                                              AppIcons.money,
                                              size: 16,
                                              color: Colors.green[700],
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              '\$${order.totalPrice.toStringAsFixed(2)}',
                                              style: GoogleFonts.poppins(
                                                fontWeight: FontWeight.w600,
                                                fontSize: 16,
                                                color: Colors.green[700],
                                              ),
                                            ),
                                          ],
                                        ),
                                        Row(
                                          children: [
                                            _buildActionButton(
                                              icon: AppIcons.visibility,
                                              label: 'View',
                                              onTap: () {
                                                _viewOrderDetails(order);
                                              },
                                            ),
                                            const SizedBox(width: 8),
                                            _buildActionButton(
                                              icon: AppIcons.chat,
                                              label: 'Chat',
                                              onTap: () {
                                                _openChat(order);
                                              },
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: onTap,
          hoverColor: AppTheme.accentColor.withOpacity(0.05),
          splashColor: AppTheme.accentColor.withOpacity(0.1),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppTheme.borderColor),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 16,
                  color: AppTheme.textSecondary,
                ),
                const SizedBox(width: 4),
                Text(
                  label,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return DateFormat('MMM d, yyyy').format(date);
  }

  void _viewOrderDetails(OrderModel order) {
    showDialog(
      context: context,
      builder: (context) => OrderDetailsDialog(order: order),
    );
  }

  void _openChat(OrderModel order) {
    showDialog(
      context: context,
      builder: (context) => ChatDialog(
        orderId: order.orderId!,
        buyerId: order.buyerId,
        publisherId: order.publisherId,
        orderTitle: order.websiteDomainName,
      ),
    );
  }
}

// You may need this String extension (add to your utils/helpers)
extension StringCasingExtension on String {
  String capitalizeFirstLetter() => isNotEmpty
      ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}'
      : this;
}

/// Dialog to display order details
class OrderDetailsDialog extends StatelessWidget {
  final OrderModel order;

  const OrderDetailsDialog({
    super.key,
    required this.order,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isNarrow = screenWidth < 600;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        width: isNarrow ? screenWidth * 0.9 : 600,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppTheme.cardShadow.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: AppTheme.accentColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        AppIcons.receipt,
                        color: AppTheme.accentColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Order Details',
                      style: GoogleFonts.poppins(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                  ],
                ),
                IconButton(
                  icon: Icon(AppIcons.close, color: AppTheme.textSecondary),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildDetailRow('Website', order.websiteDomainName),
            _buildDetailRow('Order ID', '#${order.orderId}'),
            _buildDetailRow(
              'Status',
              order.status.capitalizeFirstLetter(),
              valueColor: _getStatusColor(order.status.capitalizeFirstLetter()),
              icon: _getStatusIcon(order.status.capitalizeFirstLetter()),
              iconColor: _getStatusColor(order.status.capitalizeFirstLetter()),
            ),
            _buildDetailRow('Date',
                DateFormat('MMMM d, yyyy').format(order.orderDate.toDate())),
            if (order.status != 'Pending' && order.actionBy != null)
              _buildDetailRow(
                'Action',
                '${order.status} by ${order.actionBy}',
                valueColor:
                    _getStatusColor(order.status.capitalizeFirstLetter()),
              ),
            _buildDetailRow(
              'Amount',
              '\$${order.totalPrice.toStringAsFixed(2)}',
              valueColor: Colors.green[700],
              icon: AppIcons.money,
              iconColor: Colors.green[700],
            ),
            if (order.notes != null && order.notes!.isNotEmpty)
              _buildDetailRow('Notes', order.notes!),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                OutlinedButton.icon(
                  icon: Icon(AppIcons.chat, size: 18),
                  label: const Text('Chat with Publisher'),
                  onPressed: () {
                    Navigator.pop(context);
                    showDialog(
                      context: context,
                      builder: (context) => ChatDialog(
                        orderId: order.orderId!,
                        buyerId: order.buyerId,
                        publisherId: order.publisherId,
                        orderTitle: order.websiteDomainName,
                      ),
                    );
                  },
                  style: ButtonStyle(
                    foregroundColor:
                        MaterialStateProperty.all(AppTheme.accentColor),
                    side: MaterialStateProperty.all(
                        BorderSide(color: AppTheme.accentColor)),
                    padding: MaterialStateProperty.all(
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    shape: MaterialStateProperty.all(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    backgroundColor: MaterialStateProperty.resolveWith<Color>(
                      (states) {
                        if (states.contains(MaterialState.hovered)) {
                          return AppTheme.accentColor.withOpacity(0.05);
                        }
                        return Colors.transparent;
                      },
                    ),
                    overlayColor: MaterialStateProperty.resolveWith<Color>(
                      (states) {
                        if (states.contains(MaterialState.pressed)) {
                          return AppTheme.accentColor.withOpacity(0.1);
                        }
                        return Colors.transparent;
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ButtonStyle(
                    backgroundColor:
                        MaterialStateProperty.all(AppTheme.accentColor),
                    foregroundColor: MaterialStateProperty.all(Colors.white),
                    padding: MaterialStateProperty.all(
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    shape: MaterialStateProperty.all(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    elevation: MaterialStateProperty.all(0),
                    overlayColor: MaterialStateProperty.resolveWith<Color>(
                      (states) {
                        if (states.contains(MaterialState.hovered)) {
                          return Colors.white.withOpacity(0.1);
                        }
                        if (states.contains(MaterialState.pressed)) {
                          return Colors.black.withOpacity(0.05);
                        }
                        return Colors.transparent;
                      },
                    ),
                  ),
                  child: const Text('Close'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value, {
    Color? valueColor,
    IconData? icon,
    Color? iconColor,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppTheme.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Row(
              children: [
                if (icon != null) ...[
                  Icon(icon,
                      size: 16, color: iconColor ?? AppTheme.textPrimary),
                  const SizedBox(width: 6),
                ],
                Flexible(
                  child: Text(
                    value,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: valueColor ?? AppTheme.textPrimary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Completed':
        return Colors.green;
      case 'Pending':
        return Colors.orange;
      case 'In Progress':
        return Colors.purple;
      case 'Cancelled':
        return Colors.red;
      default:
        return Colors.blue;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'Completed':
        return AppIcons.success;
      case 'Pending':
        return AppIcons.time;
      case 'In Progress':
        return Icons.sync;
      case 'Cancelled':
        return AppIcons.error;
      default:
        return AppIcons.help;
    }
  }
}
