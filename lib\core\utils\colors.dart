import 'package:flutter/material.dart';
import 'package:guest_posts/core/theme/app_theme.dart';

/// Legacy color class that now forwards to the new AppTheme
/// This ensures backward compatibility with existing code
@Deprecated('Use AppTheme instead')
class AppColors {
  // Forward all colors to AppTheme
  static Color get backgroundColor => AppTheme.backgroundColor;
  static Color get componentBackColor => AppTheme.componentBackColor;
  static Color get black => AppTheme.textPrimary;
  static Color get greyBack => AppTheme.lightGrey;
  static Color get greyText => AppTheme.textSecondary;
  static Color get secondryColor => AppTheme.secondaryColor;
  static Color get clickBlue => AppTheme.accentColor;
  static const Color primaryColor = AppTheme.primaryColor;
  static const Color accentColor = AppTheme.accentColor;
  static const Color successColor = AppTheme.successColor;
  static const Color errorColor = AppTheme.errorColor;
  static const Color warningColor = AppTheme.warningColor;
  static const Color infoColor = AppTheme.infoColor;
  static const Color textPrimary = AppTheme.textPrimary;
  static const Color textSecondary = AppTheme.textSecondary;
  static const Color primaryButtonColor = AppTheme.primaryColor;
  static const Color borderColor = AppTheme.borderColor;
  static const Color lightGrey = AppTheme.lightGrey;
  static const Color cardShadow = AppTheme.cardShadow;

  // Gradients
  static const LinearGradient primaryGradient = AppTheme.primaryGradient;
}
