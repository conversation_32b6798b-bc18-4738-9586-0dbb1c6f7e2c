import 'dart:convert';
import 'dart:typed_data';

import 'package:csv/csv.dart';
import 'package:flutter/foundation.dart';
import 'package:universal_html/html.dart' as html;
import 'package:path_provider/path_provider.dart';
import 'dart:io';

import 'package:guest_posts/core/models/website_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class CSVHelper {
  // Generate CSV template for website import
  static String generateWebsiteTemplateCSV() {
    final List<List<dynamic>> csvData = [];
    
    // Header row
    csvData.add([
      'url',
      'domainName',
      'language',
      'isSponsored',
      'categories',
      'da',
      'dr',
      'traffic',
      'basePricing',
      'specialTopicsAdditionalPrice',
      'hasSpecialTopicsPricing',
      'backlinkType',
      'dofollowCost',
      'allowedTopics',
      'disallowedTopics',
      'wordCountMin',
      'wordCountMax',
      'maxLinks',
      'contentType',
      'contactEmail',
      'notes',
      'country',
      'spamScore',
    ]);
    
    // Example row
    csvData.add([
      'example.com',
      'example',
      'English',
      'false',
      'Technology, Business',
      '30',
      '40',
      '5000',
      '100',
      '50',
      'true',
      'nofollow',
      '75',
      'Technology, Business, Marketing',
      'Gambling, Adult',
      '500',
      '1500',
      '3',
      'article',
      '<EMAIL>',
      'Example website notes',
      'United States',
      '25',
    ]);
    
    String csv = const ListToCsvConverter().convert(csvData);
    return csv;
  }
  
  // Download CSV template in web
  static void downloadCSVTemplate() {
    final csvData = generateWebsiteTemplateCSV();
    
    if (kIsWeb) {
      // For web platform
      final bytes = utf8.encode(csvData);
      final blob = html.Blob([bytes]);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute('download', 'website_template.csv')
        ..click();
      html.Url.revokeObjectUrl(url);
    } else {
      // For mobile/desktop platforms
      _saveCSVFile(csvData, 'website_template.csv');
    }
  }
  
  // Save CSV file for mobile/desktop
  static Future<void> _saveCSVFile(String csvData, String fileName) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final path = '${directory.path}/$fileName';
      final file = File(path);
      await file.writeAsString(csvData);
    } catch (e) {
      print('Error saving CSV file: $e');
    }
  }
  
  // Parse CSV data to list of maps
  static List<Map<String, dynamic>> parseCSV(String csvData) {
    List<List<dynamic>> rowsAsListOfValues = const CsvToListConverter().convert(csvData);
    
    // Extract headers from the first row
    List<String> headers = rowsAsListOfValues[0].map((header) => header.toString()).toList();
    
    // Convert each row to a map using the headers
    List<Map<String, dynamic>> result = [];
    for (int i = 1; i < rowsAsListOfValues.length; i++) {
      Map<String, dynamic> rowMap = {};
      for (int j = 0; j < headers.length; j++) {
        if (j < rowsAsListOfValues[i].length) {
          rowMap[headers[j]] = rowsAsListOfValues[i][j];
        }
      }
      result.add(rowMap);
    }
    
    return result;
  }
  
  // Validate website data from CSV
  static Map<String, dynamic> validateWebsiteData(Map<String, dynamic> data) {
    Map<String, dynamic> result = {
      'isValid': true,
      'errors': <String>[],
      'data': <String, dynamic>{},
    };
    
    // Required fields validation
    List<String> requiredFields = [
      'url', 'domainName', 'language', 'da', 'dr', 'traffic', 
      'basePricing', 'wordCountMin', 'wordCountMax', 'maxLinks', 'country'
    ];
    
    for (String field in requiredFields) {
      if (data[field] == null || data[field].toString().isEmpty) {
        result['isValid'] = false;
        result['errors'].add('Missing required field: $field');
      }
    }
    
    // Process and convert data types
    try {
      if (result['isValid']) {
        // Convert string values to appropriate types
        result['data'] = {
          'url': data['url'].toString(),
          'domainName': data['domainName'].toString(),
          'language': data['language'].toString(),
          'isSponsored': data['isSponsored'].toString().toLowerCase() == 'true',
          'categories': data['categories'].toString().split(',').map((e) => e.trim()).toList(),
          'da': int.parse(data['da'].toString()),
          'dr': int.parse(data['dr'].toString()),
          'traffic': int.parse(data['traffic'].toString()),
          'basePricing': double.parse(data['basePricing'].toString()),
          'specialTopicsAdditionalPrice': data['specialTopicsAdditionalPrice'] != null ? 
              double.parse(data['specialTopicsAdditionalPrice'].toString()) : 0.0,
          'hasSpecialTopicsPricing': data['hasSpecialTopicsPricing'].toString().toLowerCase() == 'true',
          'backlinkType': data['backlinkType']?.toString() ?? 'nofollow',
          'dofollowCost': data['dofollowCost'] != null ? 
              double.parse(data['dofollowCost'].toString()) : 0.0,
          'allowedTopics': data['allowedTopics'].toString().split(',').map((e) => e.trim()).toList(),
          'disallowedTopics': data['disallowedTopics'].toString().split(',').map((e) => e.trim()).toList(),
          'wordCountMin': int.parse(data['wordCountMin'].toString()),
          'wordCountMax': int.parse(data['wordCountMax'].toString()),
          'maxLinks': int.parse(data['maxLinks'].toString()),
          'contentType': data['contentType']?.toString().split(',').map((e) => e.trim()).toList() ?? ['article'],
          'contactEmail': data['contactEmail']?.toString() ?? '',
          'notes': data['notes']?.toString() ?? '',
          'country': data['country'].toString(),
          'spamScore': data['spamScore'] != null ? double.parse(data['spamScore'].toString()) : null,
        };
      }
    } catch (e) {
      result['isValid'] = false;
      result['errors'].add('Data conversion error: $e');
    }
    
    return result;
  }
  
  // Convert validated data to WebsiteModel
  static WebsiteModel convertToWebsiteModel(Map<String, dynamic> data, String publisherId) {
    return WebsiteModel(
      websiteId: '',
      url: data['url'],
      domainName: data['domainName'],
      language: data['language'],
      publisherId: publisherId,
      isSponsored: data['isSponsored'],
      categories: data['categories'],
      da: data['da'],
      dr: data['dr'],
      traffic: data['traffic'],
      pricing: data['basePricing'],
      basePricing: data['basePricing'],
      specialTopicsAdditionalPrice: data['specialTopicsAdditionalPrice'],
      hasSpecialTopicsPricing: data['hasSpecialTopicsPricing'],
      backlinkType: data['backlinkType'],
      dofollowCost: data['dofollowCost'],
      allowedTopics: data['allowedTopics'],
      disallowedTopics: data['disallowedTopics'],
      wordCountMin: data['wordCountMin'],
      wordCountMax: data['wordCountMax'],
      maxLinks: data['maxLinks'],
      status: 'Pending',
      submissionDate: Timestamp.now(),
      createdAt: Timestamp.now(),
      lastUpdated: Timestamp.now(),
      isActive: true,
      contentType: data['contentType'],
      contactEmail: data['contactEmail'],
      notes: data['notes'],
      viewsCount: 0,
      clicksCount: 0,
      country: data['country'],
      adminWebsite: false,
      spamScore: data['spamScore'],
    );
  }
}
