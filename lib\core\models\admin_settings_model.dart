import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:guest_posts/core/models/offer_model.dart';

class AdminSettings {
  final double paypalFeePercentage;
  final double platformFee;
  final double razorpayFeePercentage;
  final double minimumPayout;
  final List<OfferModel> offers;

  AdminSettings({
    required this.paypalFeePercentage,
    required this.platformFee,
    required this.razorpayFeePercentage,
    required this.minimumPayout,
    required this.offers,
  });

  factory AdminSettings.fromMap(Map<String, dynamic> map) {
    return AdminSettings(
      paypalFeePercentage:
          (map['paypalFeePercentage'] as num?)?.toDouble() ?? 0.04,
      platformFee: (map['platformFee'] as num?)?.toDouble() ?? 0.039,
      razorpayFeePercentage:
          (map['razorpayFeePercentage'] as num?)?.toDouble() ?? 0.04,
      minimumPayout: (map['minimumPayout'] as num?)?.toDouble() ?? 60.0,
      offers: [],
    );
  }

  factory AdminSettings.defaultSettings() {
    return AdminSettings(
      paypalFeePercentage: 0.04,
      platformFee: 0.039,
      razorpayFeePercentage: 0.04,
      minimumPayout: 60.0,
      offers: [],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'paypalFeePercentage': paypalFeePercentage,
      'platformFee': platformFee,
      'razorpayFeePercentage': razorpayFeePercentage,
      'minimumPayout': minimumPayout,
    };
  }
}

class AdminService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get admin settings
  Stream<AdminSettings> getAdminSettings() {
    return _firestore
        .collection('settings')
        .doc('defaults')
        .snapshots()
        .map((snapshot) => AdminSettings.fromMap(snapshot.data() ?? {}));
  }

  // Get active offers
  Stream<List<OfferModel>> getActiveOffers() {
    return _firestore
        .collection('admin_settings')
        .doc('offers')
        .collection('active_offers')
        .where('isActive', isEqualTo: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => OfferModel.fromMap(doc.data(), doc.id))
            .toList());
  }

  // Add a new offer
  Future<void> addOffer(OfferModel offer) async {
    await _firestore
        .collection('admin_settings')
        .doc('offers')
        .collection('active_offers')
        .add(offer.toMap());
  }

  // Update an existing offer
  Future<void> updateOffer(OfferModel offer) async {
    await _firestore
        .collection('admin_settings')
        .doc('offers')
        .collection('active_offers')
        .doc(offer.id)
        .update(offer.toMap());
  }

  // Delete an offer
  Future<void> deleteOffer(String offerId) async {
    await _firestore
        .collection('admin_settings')
        .doc('offers')
        .collection('active_offers')
        .doc(offerId)
        .delete();
  }

  // Toggle offer active status
  Future<void> toggleOfferStatus(String offerId, bool isActive) async {
    await _firestore
        .collection('admin_settings')
        .doc('offers')
        .collection('active_offers')
        .doc(offerId)
        .update({'isActive': isActive});
  }
}
