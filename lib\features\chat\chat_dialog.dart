import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:guest_posts/core/theme/app_theme.dart';
import 'package:guest_posts/core/utils/app_icons.dart';
import 'package:guest_posts/core/utils/toast_helper.dart';
import 'package:google_fonts/google_fonts.dart';

class ChatDialog extends StatefulWidget {
  final String orderId;
  final String buyerId;
  final String publisherId;
  final String orderTitle;

  const ChatDialog({
    super.key,
    required this.orderId,
    required this.buyerId,
    required this.publisherId,
    required this.orderTitle,
  });

  @override
  State<ChatDialog> createState() => _ChatDialogState();
}

class _ChatDialogState extends State<ChatDialog> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  String? _chatId;

  @override
  void initState() {
    super.initState();
    _scrollToBottom();
    _getChatId();
  }

  Future<void> _getChatId() async {
    try {
      // Check if chat exists for this order
      final chatQuery = await _firestore
          .collection('chats')
          .where('orderId', isEqualTo: widget.orderId)
          .limit(1)
          .get();

      if (chatQuery.docs.isNotEmpty) {
        _chatId = chatQuery.docs.first.id;
        _markChatAsRead();
      } else {
        // Create a new chat document if it doesn't exist
        final chatDoc = await _firestore.collection('chats').add({
          'orderId': widget.orderId,
          'orderTitle': widget.orderTitle,
          'participants': [widget.buyerId, widget.publisherId],
          'lastMessage': null,
          'lastMessageTime': null,
          'unreadFor': [],
          'createdAt': FieldValue.serverTimestamp(),
        });
        _chatId = chatDoc.id;
      }
    } catch (e) {
      if (mounted) {
        ToastHelper.showError('Error initializing chat: ${e.toString()}');
      }
    }
  }

  Future<void> _markChatAsRead() async {
    if (_chatId == null) return;

    try {
      final currentUserId = _auth.currentUser?.uid;
      if (currentUserId == null) return;

      // Get the current chat document
      final chatDoc = await _firestore.collection('chats').doc(_chatId).get();
      final chatData = chatDoc.data();

      if (chatData != null) {
        final List<dynamic> unreadFor = chatData['unreadFor'] ?? [];

        // If current user is in the unreadFor list, remove them
        if (unreadFor.contains(currentUserId)) {
          await _firestore.collection('chats').doc(_chatId).update({
            'unreadFor': FieldValue.arrayRemove([currentUserId]),
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ToastHelper.showError('Error marking chat as read: ${e.toString()}');
      }
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _sendMessage() async {
    if (_messageController.text.trim().isEmpty) return;

    final user = _auth.currentUser;
    if (user == null) return;

    final messageText = _messageController.text.trim();
    final message = {
      'text': messageText,
      'senderId': user.uid,
      'timestamp': FieldValue.serverTimestamp(),
    };

    try {
      // Add message to the order's messages collection
      await _firestore
          .collection('orders')
          .doc(widget.orderId)
          .collection('messages')
          .add(message);

      // If we don't have a chat ID yet, get or create one
      if (_chatId == null) {
        await _getChatId();
      }

      // Determine the recipient ID (the other participant)
      final recipientId =
          user.uid == widget.buyerId ? widget.publisherId : widget.buyerId;

      // Update the chat document with the latest message info
      if (_chatId != null) {
        await _firestore.collection('chats').doc(_chatId).update({
          'lastMessage': messageText,
          'lastMessageTime': FieldValue.serverTimestamp(),
          'unreadFor': FieldValue.arrayUnion([recipientId]),
        });
      }

      _messageController.clear();
      _scrollToBottom();
    } catch (e) {
      if (mounted) {
        ToastHelper.showError('Error sending message: ${e.toString()}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isNarrow = screenWidth < 600;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        width: isNarrow ? screenWidth * 0.9 : 600,
        height: isNarrow ? screenWidth * 1.2 : 600,
        padding: const EdgeInsets.all(0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppTheme.cardShadow.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              decoration: BoxDecoration(
                color: AppTheme.accentColor.withOpacity(0.05),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                border: Border(
                  bottom: BorderSide(
                    color: AppTheme.borderColor.withOpacity(0.1),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: AppTheme.accentColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          AppIcons.chat,
                          color: AppTheme.accentColor,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Order Chat',
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.textPrimary,
                            ),
                          ),
                          Text(
                            widget.orderTitle,
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: AppTheme.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  IconButton(
                    icon: Icon(AppIcons.close, color: AppTheme.textSecondary),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),

            // Chat messages
            Expanded(
              child: StreamBuilder<QuerySnapshot>(
                stream: _firestore
                    .collection('orders')
                    .doc(widget.orderId)
                    .collection('messages')
                    .orderBy('timestamp', descending: false)
                    .snapshots(),
                builder: (context, snapshot) {
                  if (snapshot.hasError) {
                    return Center(
                      child: Text(
                        'Error loading messages',
                        style: GoogleFonts.poppins(
                          color: Colors.red,
                        ),
                      ),
                    );
                  }

                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  final messages = snapshot.data?.docs ?? [];
                  final currentUser = _auth.currentUser;

                  if (messages.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            AppIcons.chat,
                            size: 64,
                            color: Colors.grey[300],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No messages yet',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              color: AppTheme.textSecondary,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Start the conversation',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: AppTheme.textLight,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: messages.length,
                    itemBuilder: (context, index) {
                      final message =
                          messages[index].data() as Map<String, dynamic>;
                      final isMe = message['senderId'] == currentUser?.uid;
                      final timestamp = message['timestamp'] as Timestamp?;
                      final time = timestamp != null
                          ? '${timestamp.toDate().hour}:${timestamp.toDate().minute.toString().padLeft(2, '0')}'
                          : '';

                      return Align(
                        alignment:
                            isMe ? Alignment.centerRight : Alignment.centerLeft,
                        child: Container(
                          constraints: BoxConstraints(
                            maxWidth: MediaQuery.of(context).size.width * 0.7,
                          ),
                          margin: const EdgeInsets.only(bottom: 12),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 10,
                          ),
                          decoration: BoxDecoration(
                            color:
                                isMe ? AppTheme.accentColor : Colors.grey[100],
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 5,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                message['text'] ?? '',
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  color: isMe
                                      ? Colors.white
                                      : AppTheme.textPrimary,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                time,
                                style: GoogleFonts.poppins(
                                  fontSize: 10,
                                  color: isMe
                                      ? Colors.white.withOpacity(0.7)
                                      : AppTheme.textLight,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),

            // Message input
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, -1),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _messageController,
                      decoration: InputDecoration(
                        hintText: 'Type a message...',
                        hintStyle: GoogleFonts.poppins(
                          color: AppTheme.textLight,
                        ),
                        filled: true,
                        fillColor: Colors.grey[100],
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(24),
                          borderSide: BorderSide.none,
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: AppTheme.textPrimary,
                      ),
                      maxLines: null,
                      onSubmitted: (_) => _sendMessage(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Material(
                    color: AppTheme.accentColor,
                    borderRadius: BorderRadius.circular(24),
                    child: InkWell(
                      onTap: _sendMessage,
                      borderRadius: BorderRadius.circular(24),
                      hoverColor: Colors.white.withOpacity(0.1),
                      splashColor: Colors.white.withOpacity(0.2),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        child: const Icon(
                          Icons.send,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
